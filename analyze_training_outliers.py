#!/usr/bin/env python3
"""
🔍 ANALYZE TRAINING DATA OUTLIERS
Check for extreme values in training data that may cause over-prediction
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

print("🔍 ANALYZING TRAINING DATA FOR OUTLIERS")
print("=" * 60)

def analyze_prop_data(prop_type):
    """Analyze training data for a specific prop type"""
    
    file_path = f"data/real_wnba_{prop_type}_training_data.csv"
    
    if not Path(file_path).exists():
        print(f"❌ File not found: {file_path}")
        return None
    
    print(f"\n📊 ANALYZING {prop_type.upper()} TRAINING DATA:")
    print("-" * 40)
    
    # Load data
    df = pd.read_csv(file_path)
    print(f"📄 Loaded {len(df)} records")
    
    # Check if we have the target column
    target_col = f"{prop_type}_target" if f"{prop_type}_target" in df.columns else prop_type
    
    if target_col not in df.columns:
        print(f"❌ Target column '{target_col}' not found")
        print(f"Available columns: {list(df.columns)}")
        return None
    
    targets = df[target_col].dropna()
    
    # Basic statistics
    print(f"📈 RAW TARGET STATISTICS:")
    print(f"   Count: {len(targets)}")
    print(f"   Mean:  {targets.mean():.2f}")
    print(f"   Std:   {targets.std():.2f}")
    print(f"   Min:   {targets.min():.2f}")
    print(f"   Max:   {targets.max():.2f}")
    print(f"   Median: {targets.median():.2f}")
    
    # Percentiles
    percentiles = [50, 75, 90, 95, 99, 99.5, 99.9]
    print(f"\n📊 PERCENTILES:")
    for p in percentiles:
        value = np.percentile(targets, p)
        print(f"   {p:4.1f}%: {value:6.2f}")
    
    # Check for games_played column to convert to per-game
    if 'games_played' in df.columns:
        games_played = df['games_played'].fillna(1)
        # Filter out low games (< 5) as in our training pipeline
        valid_games = games_played >= 5
        
        if valid_games.sum() > 0:
            per_game_targets = targets[valid_games] / games_played[valid_games]
            per_game_targets = per_game_targets.dropna()
            
            print(f"\n🎯 PER-GAME STATISTICS (games_played >= 5):")
            print(f"   Count: {len(per_game_targets)}")
            print(f"   Mean:  {per_game_targets.mean():.2f}")
            print(f"   Std:   {per_game_targets.std():.2f}")
            print(f"   Min:   {per_game_targets.min():.2f}")
            print(f"   Max:   {per_game_targets.max():.2f}")
            print(f"   Median: {per_game_targets.median():.2f}")
            
            # Per-game percentiles
            print(f"\n📊 PER-GAME PERCENTILES:")
            for p in percentiles:
                value = np.percentile(per_game_targets, p)
                print(f"   {p:4.1f}%: {value:6.2f}")
            
            # Check for outliers (values > 99th percentile)
            p99 = np.percentile(per_game_targets, 99)
            outliers = per_game_targets[per_game_targets > p99]
            
            print(f"\n🚨 OUTLIERS (> 99th percentile = {p99:.2f}):")
            print(f"   Count: {len(outliers)}")
            if len(outliers) > 0:
                print(f"   Values: {sorted(outliers.values, reverse=True)[:10]}")  # Top 10 outliers
            
            # Realistic WNBA bounds check
            realistic_bounds = {
                'points': 28.0,
                'rebounds': 15.0,
                'assists': 10.0,
                'steals': 3.0,
                'blocks': 3.0,
                'threes': 4.0
            }
            
            bound = realistic_bounds.get(prop_type, 25.0)
            extreme_outliers = per_game_targets[per_game_targets > bound]
            
            print(f"\n⚠️  EXTREME OUTLIERS (> {bound} per game):")
            print(f"   Count: {len(extreme_outliers)}")
            print(f"   Percentage: {len(extreme_outliers)/len(per_game_targets)*100:.2f}%")
            if len(extreme_outliers) > 0:
                print(f"   Values: {sorted(extreme_outliers.values, reverse=True)[:5]}")
            
            return {
                'prop_type': prop_type,
                'total_records': len(targets),
                'per_game_records': len(per_game_targets),
                'per_game_mean': per_game_targets.mean(),
                'per_game_max': per_game_targets.max(),
                'p99': p99,
                'outliers_count': len(outliers),
                'extreme_outliers_count': len(extreme_outliers),
                'extreme_outliers_pct': len(extreme_outliers)/len(per_game_targets)*100
            }
    
    return None

def main():
    """Analyze all prop types"""
    
    prop_types = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
    results = []
    
    for prop_type in prop_types:
        result = analyze_prop_data(prop_type)
        if result:
            results.append(result)
    
    # Summary
    print(f"\n" + "=" * 60)
    print(f"📋 OUTLIER ANALYSIS SUMMARY:")
    print("=" * 60)
    
    if results:
        print(f"{'Prop':<8} {'Records':<8} {'Mean':<6} {'Max':<6} {'P99':<6} {'Outliers':<8} {'Extreme':<7} {'Extreme%':<8}")
        print("-" * 60)
        
        for r in results:
            print(f"{r['prop_type']:<8} {r['per_game_records']:<8} {r['per_game_mean']:<6.1f} {r['per_game_max']:<6.1f} {r['p99']:<6.1f} {r['outliers_count']:<8} {r['extreme_outliers_count']:<7} {r['extreme_outliers_pct']:<8.1f}")
    
    print(f"\n🎯 RECOMMENDATIONS:")
    print(f"1. Remove or cap extreme outliers during training")
    print(f"2. Use realistic bounds at inference (already implemented)")
    print(f"3. Consider log transformation for highly skewed distributions")
    print(f"4. Add more regularization if outliers are causing overfitting")

if __name__ == "__main__":
    main()
