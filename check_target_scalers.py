#!/usr/bin/env python3
"""
Check target scaler parameters for all models
"""

import torch

prop_types = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']

print("🔍 TARGET SCALER PARAMETERS ANALYSIS")
print("=" * 60)

for prop_type in prop_types:
    model_path = f"models/player_props/nba_points/best_{prop_type}_model.pt"
    
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        target_params = checkpoint.get('target_scaler_params', {})
        
        print(f"\n📊 {prop_type.upper()} MODEL:")
        if target_params:
            mean = target_params.get('mean_', [None])[0] if target_params.get('mean_') else None
            scale = target_params.get('scale_', [None])[0] if target_params.get('scale_') else None
            print(f"   Mean: {mean:.4f}" if mean is not None else "   Mean: NOT FOUND")
            print(f"   Scale: {scale:.4f}" if scale is not None else "   Scale: NOT FOUND")
            
            # Calculate what a raw output of 0.0 would unscale to
            if mean is not None and scale is not None:
                unscaled_zero = (0.0 * scale) + mean
                print(f"   Raw 0.0 → Unscaled: {unscaled_zero:.2f}")
                
            # Calculate what a raw output of 18.88 (rebounds) would unscale to
            if prop_type == 'rebounds' and mean is not None and scale is not None:
                unscaled_high = (18.88 * scale) + mean
                print(f"   Raw 18.88 → Unscaled: {unscaled_high:.2f}")
        else:
            print("   ❌ NO TARGET SCALER PARAMETERS FOUND")
            
    except Exception as e:
        print(f"\n❌ Error loading {prop_type} model: {e}")

print("\n" + "=" * 60)
