#!/usr/bin/env python3
"""
Debug script to check what's happening in the data loading pipeline
"""

import pandas as pd
import numpy as np
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_data_loading():
    """Debug the data loading and conversion process"""
    
    # Load the points training data
    real_data_path = "data/real_wnba_points_training_data.csv"
    logger.info(f"🔍 Loading data from: {real_data_path}")
    
    real_wnba_data = pd.read_csv(real_data_path)
    logger.info(f"✅ Loaded {len(real_wnba_data)} records")
    
    # Check the first few rows
    logger.info("📊 First 5 rows of raw data:")
    print(real_wnba_data[['points', 'games_played', 'target']].head())
    
    # Check target statistics
    logger.info(f"📊 Raw target statistics:")
    logger.info(f"   Min: {real_wnba_data['target'].min():.2f}")
    logger.info(f"   Max: {real_wnba_data['target'].max():.2f}")
    logger.info(f"   Mean: {real_wnba_data['target'].mean():.2f}")
    logger.info(f"   Median: {real_wnba_data['target'].median():.2f}")
    
    # Check if target matches points column
    target_matches_points = (real_wnba_data['target'] == real_wnba_data['points']).all()
    logger.info(f"📊 Target column matches points column: {target_matches_points}")
    
    # Simulate the conversion logic from the training pipeline
    processed_data = real_wnba_data.copy()
    
    # Rename target column to prop_target for consistency
    if 'target' in processed_data.columns:
        processed_data['prop_target'] = processed_data['target']
        processed_data = processed_data.drop('target', axis=1)
    
    # Filter out players with very few games (< 5 games)
    initial_count = len(processed_data)
    processed_data = processed_data[processed_data['games_played'] >= 5]
    filtered_count = len(processed_data)
    logger.info(f"📊 Filtered out {initial_count - filtered_count} records with < 5 games")
    
    # Check if conversion is needed
    prop_type = 'points'
    max_raw_value = processed_data[prop_type].max()
    logger.info(f"📊 Max raw {prop_type} value: {max_raw_value}")
    
    # Per-game maximums for determining if conversion is needed
    per_game_maximums = {
        'points': 35, 'rebounds': 15, 'assists': 10,
        'steals': 3, 'blocks': 3, 'threes': 6
    }
    reasonable_max = per_game_maximums.get(prop_type, 50)
    logger.info(f"📊 Reasonable per-game maximum for {prop_type}: {reasonable_max}")
    
    if max_raw_value > reasonable_max:
        logger.info(f"🔄 Values appear to be season totals, converting to per-game...")
        # Convert to per-game
        processed_data['prop_target'] = processed_data[prop_type] / processed_data['games_played']
        logger.info(f"📊 Converted {prop_type} season totals to per-game averages")
    else:
        logger.info(f"📊 Values appear to already be per-game")
        processed_data['prop_target'] = processed_data[prop_type]
    
    # Check converted target statistics
    logger.info(f"📊 Converted target statistics:")
    logger.info(f"   Min: {processed_data['prop_target'].min():.2f}")
    logger.info(f"   Max: {processed_data['prop_target'].max():.2f}")
    logger.info(f"   Mean: {processed_data['prop_target'].mean():.2f}")
    logger.info(f"   Median: {processed_data['prop_target'].median():.2f}")
    
    # Show some examples
    logger.info("📊 Examples of conversion:")
    examples = processed_data[['points', 'games_played', 'prop_target']].head(10)
    print(examples)
    
    # Check A'ja Wilson specifically (should be first row)
    if len(processed_data) > 0:
        first_row = processed_data.iloc[0]
        logger.info(f"📊 First player example:")
        logger.info(f"   Points (season): {first_row['points']}")
        logger.info(f"   Games played: {first_row['games_played']}")
        logger.info(f"   Per-game target: {first_row['prop_target']:.2f}")
        logger.info(f"   Manual calculation: {first_row['points'] / first_row['games_played']:.2f}")

if __name__ == "__main__":
    debug_data_loading()
