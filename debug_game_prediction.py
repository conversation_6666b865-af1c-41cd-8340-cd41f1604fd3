#!/usr/bin/env python3
"""Debug script for game prediction data creation."""

import pandas as pd
import numpy as np
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_game_prediction():
    """Debug the game prediction data creation process."""
    data_dir = Path("data")
    
    # Load team features
    features_path = data_dir / "complete_real_wnba_features_with_metadata.csv"
    df = pd.read_csv(features_path)
    
    # Group by team and season to create team-level features
    team_features = df.groupby(['team_abbreviation', 'season']).agg({
        'points': 'mean',
        'rebounds': 'mean',
        'assists': 'mean',
        'steals': 'mean',
        'blocks': 'mean',
        'threes': 'mean',
        'field_goal_percentage': 'mean',
        'free_throw_percentage': 'mean',
        'total_stats': 'mean',
        'defensive_stats': 'mean',
        'offensive_stats': 'mean'
    }).reset_index()
    
    logger.info(f"Team features: {len(team_features)} teams")
    logger.info(f"Teams: {sorted(team_features['team_abbreviation'].unique())}")
    logger.info(f"Seasons: {sorted(team_features['season'].unique())}")
    
    # Load real WNBA games
    game_files = [
        "wnba_games_2023.csv"
    ]
    
    all_games = []
    for file_name in game_files:
        file_path = data_dir / file_name
        if file_path.exists():
            df_games = pd.read_csv(file_path)
            all_games.append(df_games)
            logger.info(f"Loaded {len(df_games)} games from {file_name}")
    
    if not all_games:
        logger.error("No game files found!")
        return
        
    real_games = pd.concat(all_games, ignore_index=True)
    logger.info(f"Total games: {len(real_games)}")
    logger.info(f"Game teams: {sorted(real_games['TEAM_ABBREVIATION'].unique())}")
    logger.info(f"Game seasons: {sorted(real_games['season'].unique())}")
    
    # Group games by GAME_ID
    game_groups = real_games.groupby('GAME_ID')
    logger.info(f"Unique games: {len(game_groups)}")
    
    # Check first few games
    processed = 0
    for game_id, game_group in list(game_groups)[:5]:
        logger.info(f"\nGame {game_id}: {len(game_group)} teams")
        if len(game_group) == 2:
            team1 = game_group.iloc[0]
            team2 = game_group.iloc[1]
            
            logger.info(f"  Team 1: {team1['TEAM_ABBREVIATION']} - {team1['MATCHUP']} - {team1['WL']}")
            logger.info(f"  Team 2: {team2['TEAM_ABBREVIATION']} - {team2['MATCHUP']} - {team2['WL']}")
            
            # Determine home/away
            matchup1 = team1['MATCHUP']
            season_raw = team1.get('season', 2023)
            season_mapping = {2023: 10, 2022: 10, 2021: 10, 2020: 10, 2024: 10, 2025: 10}
            season = season_mapping.get(season_raw, 10)
            
            if '@' in matchup1:
                away_team = team1['TEAM_ABBREVIATION']
                home_team = team2['TEAM_ABBREVIATION']
            else:
                home_team = team1['TEAM_ABBREVIATION']
                away_team = team2['TEAM_ABBREVIATION']
                
            logger.info(f"  Home: {home_team}, Away: {away_team}, Season: {season}")
            
            # Check team stats
            home_stats = team_features[
                (team_features['team_abbreviation'] == home_team) &
                (team_features['season'] == season)
            ]
            away_stats = team_features[
                (team_features['team_abbreviation'] == away_team) &
                (team_features['season'] == season)
            ]
            
            logger.info(f"  Home stats found: {len(home_stats)}")
            logger.info(f"  Away stats found: {len(away_stats)}")
            
            if len(home_stats) > 0 and len(away_stats) > 0:
                processed += 1
                logger.info(f"  ✅ Game processed successfully!")
            else:
                logger.info(f"  ❌ Missing team stats")
    
    logger.info(f"\nProcessed {processed} out of 5 sample games")

if __name__ == "__main__":
    debug_game_prediction()
