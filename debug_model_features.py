#!/usr/bin/env python3
"""
🔍 DEBUG MODEL FEATURES
======================

Check what features the trained models actually expect.
"""

import torch
import sys
sys.path.append('.')

def debug_model_features():
    """Debug the actual features in trained models"""
    
    print("🔍 DEBUGGING MODEL FEATURES")
    print("=" * 50)
    
    # Check all model types
    model_types = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
    
    for prop_type in model_types:
        model_path = f"models/real_basketball_models/best_{prop_type}_model.pt"
        
        print(f"\n📁 {prop_type.upper()} MODEL:")
        print("-" * 30)
        
        try:
            # Load checkpoint
            checkpoint = torch.load(model_path, map_location='cpu')
            
            print(f"✅ Loaded successfully")
            print(f"📊 Keys: {list(checkpoint.keys())}")
            
            if 'feature_list' in checkpoint:
                feature_list = checkpoint['feature_list']
                print(f"✅ Feature list found: {len(feature_list)} features")
                print(f"🔍 Features:")
                for i, feature in enumerate(feature_list):
                    print(f"  {i+1:2d}. {feature}")
            else:
                print(f"❌ No feature_list in checkpoint")
                
            if 'feature_scaler_params' in checkpoint:
                scaler_params = checkpoint['feature_scaler_params']
                print(f"✅ Scaler params found: {list(scaler_params.keys())}")
            else:
                print(f"❌ No feature_scaler_params in checkpoint")
                
        except Exception as e:
            print(f"❌ Failed to load: {e}")
    
    print(f"\n" + "=" * 50)
    print(f"🎯 FEATURE ANALYSIS COMPLETE")

if __name__ == "__main__":
    debug_model_features()
