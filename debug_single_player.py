#!/usr/bin/env python3
"""
Debug single player feature generation
"""

import sys
import os
import asyncio
import logging
import numpy as np

# Add project root to path
sys.path.insert(0, os.path.abspath('.'))

# Set up logging to see everything
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

async def debug_single_player():
    """Debug feature generation for a single player"""
    try:
        print("🔍 DEBUGGING SINGLE PLAYER FEATURE GENERATION")
        print("=" * 60)
        
        # Import services
        from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
        
        # Initialize service
        service = UnifiedNeuralPredictionService()
        await service._load_player_props_models()
        print(f"✅ Loaded {len(service.player_props_models)} player props models")
        
        # Game data
        game_data = {
            "home_team": "Minnesota Lynx",
            "away_team": "Golden State Valkyries", 
            "game_date": "2025-07-05",
            "league": "WNBA"
        }
        
        # Test single player
        player_data = {
            "name": "<PERSON>",
            "team": "Golden State Valkyries", 
            "position": "G"
        }
        
        print(f"\n🏀 Testing: {player_data['name']}")
        print(f"Team: {player_data['team']}")
        print(f"Position: {player_data['position']}")
        
        # Test points prediction
        prop_type = "points"
        print(f"\nGenerating features for {prop_type}...")
        
        # Call the feature preparation method directly
        features = service._prepare_player_features(player_data, prop_type)
        
        if features is not None:
            print(f"\n✅ SUCCESS: Generated {len(features)} features")
            print(f"Feature array shape: {features.shape}")
            print(f"Feature dtype: {features.dtype}")
            print(f"Range: {features.min():.2f} to {features.max():.2f}")
            print(f"Unique values: {len(np.unique(features))}/30")
            
            # Show all features in groups
            print(f"\nFull feature breakdown:")
            for i in range(0, len(features), 5):
                end_idx = min(i + 5, len(features))
                print(f"  [{i:2d}-{end_idx-1:2d}]: {features[i:end_idx]}")
        else:
            print("❌ FAILED: No features generated")
            
        # Now test a different player to compare
        print(f"\n" + "="*60)
        player_data2 = {
            "name": "Napheesa Collier",
            "team": "Minnesota Lynx", 
            "position": "F"
        }
        
        print(f"🏀 Testing: {player_data2['name']}")
        print(f"Team: {player_data2['team']}")
        print(f"Position: {player_data2['position']}")
        
        features2 = service._prepare_player_features(player_data2, prop_type)
        
        if features2 is not None:
            print(f"\n✅ SUCCESS: Generated {len(features2)} features")
            print(f"Range: {features2.min():.2f} to {features2.max():.2f}")
            print(f"Unique values: {len(np.unique(features2))}/30")
            
            # Compare features
            if features is not None:
                diff = np.abs(features - features2)
                print(f"\n🔍 FEATURE COMPARISON:")
                print(f"Max difference: {diff.max():.2f}")
                print(f"Mean difference: {diff.mean():.2f}")
                print(f"Number of identical features: {np.sum(diff < 0.001)}/30")
                
                # Show which features are different
                different_indices = np.where(diff > 0.001)[0]
                print(f"Different feature indices: {different_indices}")
                
                if len(different_indices) < 10:  # If few differences, show them
                    for idx in different_indices:
                        print(f"  Feature {idx}: {features[idx]:.3f} vs {features2[idx]:.3f} (diff: {diff[idx]:.3f})")
        else:
            print("❌ FAILED: No features generated for second player")

    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_single_player())
