#!/usr/bin/env python3
"""
Direct test of WNBA neural prediction system using PlayerPropsTrainingPipeline.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from pathlib import Path

def test_direct_player_props():
    """Test WNBA player props predictions directly using PlayerPropsTrainingPipeline."""
    print("🏀 Testing WNBA Player Props with Direct Pipeline...")
    
    try:
        from src.neural_cortex.player_props_neural_pipeline import PlayerPropsTrainingPipeline
        
        # Test data for A'ja <PERSON>
        player_data = {
            'player_id': 1628932,
            'player_name': "<PERSON>'ja <PERSON>",
            'team_id': 1611661319,
            'team_abbreviation': 'LVA',
            'season_avg_pts': 22.8,
            'season_avg_reb': 11.9,
            'season_avg_ast': 2.3,
            'season_avg_stl': 1.8,
            'season_avg_blk': 2.6,
            'season_avg_fg3m': 0.4,
            'games_played': 38,
            'minutes_per_game': 34.6,
            'usage_rate': 28.5,
            'true_shooting_pct': 0.578,
            'effective_fg_pct': 0.521,
            'league': 'WNBA'
        }
        
        # Convert to DataFrame
        input_df = pd.DataFrame([player_data])
        
        # Test each available WNBA model
        models_dir = Path("models/real_basketball_models")
        
        if not models_dir.exists():
            print(f"❌ Models directory not found: {models_dir}")
            return
            
        # Find WNBA models
        model_files = list(models_dir.glob("*.pt"))
        print(f"📊 Found {len(model_files)} model files")
        
        for model_file in model_files:
            if 'wnba' in model_file.name.lower() or 'real' in model_file.name.lower():
                print(f"\n🔍 Testing model: {model_file.name}")
                
                try:
                    # Use the direct prediction method
                    result_df = PlayerPropsTrainingPipeline.predict_from_checkpoint(
                        checkpoint_path=str(model_file),
                        input_df=input_df,
                        device='cpu',
                        return_confidence=True
                    )
                    
                    print(f"✅ Prediction successful!")
                    print(f"Result shape: {result_df.shape}")
                    print(f"Columns: {list(result_df.columns)}")
                    
                    # Show the prediction
                    if 'prediction' in result_df.columns:
                        prediction = result_df['prediction'].iloc[0]
                        print(f"Prediction: {prediction:.2f}")
                        
                        # Validate based on model type
                        model_name = model_file.name.lower()
                        if 'points' in model_name and not (0 <= prediction <= 40):
                            print(f"    ⚠️  WARNING: Points prediction {prediction} seems unrealistic for WNBA")
                        elif 'rebounds' in model_name and not (0 <= prediction <= 20):
                            print(f"    ⚠️  WARNING: Rebounds prediction {prediction} seems unrealistic for WNBA")
                        elif 'assists' in model_name and not (0 <= prediction <= 15):
                            print(f"    ⚠️  WARNING: Assists prediction {prediction} seems unrealistic for WNBA")
                        elif any(x in model_name for x in ['steals', 'blocks', 'threes']) and not (0 <= prediction <= 10):
                            print(f"    ⚠️  WARNING: {model_name} prediction {prediction} seems unrealistic for WNBA")
                        else:
                            print(f"    ✅ Prediction {prediction} looks reasonable for WNBA")
                    
                    if 'confidence' in result_df.columns:
                        confidence = result_df['confidence'].iloc[0]
                        print(f"Confidence: {confidence:.3f}")
                        
                except Exception as e:
                    print(f"    ❌ Error: {str(e)}")
                    
    except ImportError as e:
        print(f"❌ Import error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_model_loading():
    """Test loading individual models to check their structure."""
    print("\n🔍 Testing Model Loading...")
    
    try:
        import torch
        
        models_dir = Path("models/real_basketball_models")
        model_files = list(models_dir.glob("*.pt"))
        
        for model_file in model_files[:3]:  # Test first 3 models
            print(f"\n📊 Examining: {model_file.name}")
            
            try:
                checkpoint = torch.load(model_file, map_location='cpu')
                
                print(f"  Keys: {list(checkpoint.keys())}")
                
                if 'config' in checkpoint:
                    config = checkpoint['config']
                    print(f"  Input dim: {config.get('input_dim', 'N/A')}")
                    print(f"  Hidden dim: {config.get('hidden_dim', 'N/A')}")
                    print(f"  Prop type: {config.get('prop_type', 'N/A')}")
                    
                if 'feature_list' in checkpoint:
                    feature_list = checkpoint['feature_list']
                    print(f"  Features: {len(feature_list)} features")
                    print(f"  First 5: {feature_list[:5]}")
                else:
                    print("  ⚠️  No feature_list found (legacy model)")
                    
            except Exception as e:
                print(f"  ❌ Error loading: {e}")
                
    except Exception as e:
        print(f"❌ Error in model loading test: {e}")

def main():
    """Run all direct WNBA tests."""
    print("🚀 Starting Direct WNBA Neural Prediction Tests...")
    print("=" * 60)
    
    # Test model loading first
    test_model_loading()
    
    # Test direct predictions
    test_direct_player_props()
    
    print("\n" + "=" * 60)
    print("✅ Direct WNBA prediction tests completed!")

if __name__ == "__main__":
    main()
