#!/usr/bin/env python3
"""
🔍 INSPECT MODEL CHECKPOINT - Check the exact structure
"""

import torch
import sys

# Add project root to path
sys.path.append('.')

print("🔍 INSPECTING MODEL CHECKPOINT STRUCTURE")
print("=" * 50)

# Load the points model checkpoint
checkpoint_path = "models/player_props/nba_points/best_points_model.pt"

try:
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    print("📊 Checkpoint keys:")
    for key in checkpoint.keys():
        print(f"   - {key}")
    
    print(f"\n🎯 Target Scaler Parameters:")
    if 'target_scaler_params' in checkpoint:
        target_params = checkpoint['target_scaler_params']
        print(f"   Type: {type(target_params)}")
        for key, value in target_params.items():
            print(f"   {key}: {value} (type: {type(value)})")
    
    print(f"\n🔧 Feature Scaler Parameters:")
    if 'feature_scaler_params' in checkpoint:
        feature_params = checkpoint['feature_scaler_params']
        print(f"   Type: {type(feature_params)}")
        for key, value in feature_params.items():
            if hasattr(value, 'shape'):
                print(f"   {key}: shape={value.shape}, type={type(value)}")
            else:
                print(f"   {key}: {value} (type: {type(value)})")
    
    print(f"\n📈 Model State Dict Keys (first 10):")
    if 'model_state_dict' in checkpoint:
        model_keys = list(checkpoint['model_state_dict'].keys())
        for i, key in enumerate(model_keys[:10]):
            print(f"   {i+1}. {key}")
        if len(model_keys) > 10:
            print(f"   ... and {len(model_keys) - 10} more")
    
    print(f"\n✅ Checkpoint loaded successfully!")
    
except Exception as e:
    print(f"❌ Error loading checkpoint: {e}")
    import traceback
    traceback.print_exc()

print("=" * 50)
