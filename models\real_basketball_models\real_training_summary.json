{"points": {"prop_name": "points", "input_size": 30, "train_r2": 0.9933878837738735, "test_r2": 0.9864259293451785, "train_rmse": 11.464933901951177, "test_rmse": 16.803156310914858, "best_val_loss": 282.3460693359375, "model_path": "models\\real_basketball_models\\real_points_model.pt", "scaler_path": "models\\real_basketball_models\\real_points_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_points_scaler_params.json", "feature_count": 30}, "rebounds": {"prop_name": "rebounds", "input_size": 30, "train_r2": 0.9920149840682988, "test_r2": 0.988179989542778, "train_rmse": 5.493254439715106, "test_rmse": 6.8505863809914285, "best_val_loss": 43.63750457763672, "model_path": "models\\real_basketball_models\\real_rebounds_model.pt", "scaler_path": "models\\real_basketball_models\\real_rebounds_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_rebounds_scaler_params.json", "feature_count": 30}, "assists": {"prop_name": "assists", "input_size": 30, "train_r2": 0.994266962499699, "test_r2": 0.9924017628917824, "train_rmse": 2.9741240373712756, "test_rmse": 3.6949945289036434, "best_val_loss": 9.929293632507324, "model_path": "models\\real_basketball_models\\real_assists_model.pt", "scaler_path": "models\\real_basketball_models\\real_assists_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_assists_scaler_params.json", "feature_count": 30}, "steals": {"prop_name": "steals", "input_size": 30, "train_r2": 0.9932205782192712, "test_r2": 0.988440633046635, "train_rmse": 1.012853868347226, "test_rmse": 1.3183776742954438, "best_val_loss": 1.166353702545166, "model_path": "models\\real_basketball_models\\real_steals_model.pt", "scaler_path": "models\\real_basketball_models\\real_steals_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_steals_scaler_params.json", "feature_count": 30}, "blocks": {"prop_name": "blocks", "input_size": 30, "train_r2": 0.9980948258188265, "test_r2": 0.9972771073091213, "train_rmse": 0.41924273673148366, "test_rmse": 0.5595969214395172, "best_val_loss": 0.30354344844818115, "model_path": "models\\real_basketball_models\\real_blocks_model.pt", "scaler_path": "models\\real_basketball_models\\real_blocks_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_blocks_scaler_params.json", "feature_count": 30}, "threes": {"prop_name": "threes", "input_size": 30, "train_r2": 0.9967786431994126, "test_r2": 0.9953038783788457, "train_rmse": 0.8915079068342594, "test_rmse": 1.1047061786925672, "best_val_loss": 1.096969485282898, "model_path": "models\\real_basketball_models\\real_threes_model.pt", "scaler_path": "models\\real_basketball_models\\real_threes_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_threes_scaler_params.json", "feature_count": 30}, "game_prediction": {"model_type": "game_prediction", "input_size": 10, "train_accuracy": 0.601995587348938, "test_accuracy": 0.6017699241638184, "best_val_accuracy": 0.6084070801734924, "model_path": "models\\real_basketball_models\\real_game_model.pt", "scaler_path": "models\\real_basketball_models\\real_game_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_game_scaler_params.json", "feature_count": 10}, "team_total": {"model_type": "team_total", "input_size": 9, "train_r2": 0.8078823798249921, "test_r2": 0.7573282680692581, "train_rmse": 4.840608412382591, "test_rmse": 5.284259500026198, "best_val_loss": 0.221308633685112, "model_path": "models\\real_basketball_models\\real_team_total_model.pt", "feature_scaler_path": "models\\real_basketball_models\\real_team_total_feature_scaler.pkl", "target_scaler_path": "models\\real_basketball_models\\real_team_total_target_scaler.pkl", "feature_scaler_params_path": "models\\real_basketball_models\\real_team_total_feature_scaler_params.json", "target_scaler_params_path": "models\\real_basketball_models\\real_team_total_target_scaler_params.json", "feature_count": 9}}