#!/usr/bin/env python3
import sys
import asyncio
sys.path.append('.')

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

async def test():
    service = UnifiedNeuralPredictionService('WNBA')
    await service._load_player_props_models()

    # Test multiple players to see diversity
    game_data = {'home_team': 'Minnesota Lynx', 'away_team': 'Golden State Valkyries', 'game_date': '2025-07-05', 'league': 'WNBA'}

    # Test known players with different profiles
    test_players = [
        {'name': '<PERSON>', 'team': 'Golden State Valkyries'},  # Star scorer
        {'name': '<PERSON><PERSON><PERSON><PERSON>', 'team': 'Minnesota Lynx'},      # Elite scorer
        {'name': '<PERSON>', 'team': 'Minnesota Lynx'},       # Star rebounder
        {'name': '<PERSON>', 'team': 'Golden State Valkyries'}    # Solid role player
    ]

    for player in test_players:
        print(f"\n🏀 Testing {player['name']}...")
        result = await service.predict_unified(game_data, [player])
        if hasattr(result, 'player_props') and result.player_props:
            props = result.player_props[player['name']]
            print(f"   Points: {props['points']:.1f}, Rebounds: {props['rebounds']:.1f}, Assists: {props['assists']:.1f}")
        else:
            print("   No prediction")

asyncio.run(test())
