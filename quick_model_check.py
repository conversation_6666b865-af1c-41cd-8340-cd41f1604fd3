#!/usr/bin/env python3
"""
Quick check of one model to see if it's working
"""

import torch
import sys
sys.path.append('.')

# Load one model
model_path = "models/player_props/nba_points/best_points_model.pt"

try:
    checkpoint = torch.load(model_path, map_location='cpu')
    print("✅ Model loaded successfully")
    print(f"Keys: {list(checkpoint.keys())}")
    
    if 'metrics' in checkpoint:
        metrics = checkpoint['metrics']
        print(f"Test R²: {metrics.get('test_r2', 'N/A')}")
        print(f"Train R²: {metrics.get('train_r2', 'N/A')}")
    
    if 'target_scaler_params' in checkpoint:
        scaler = checkpoint['target_scaler_params']
        print(f"Target scaler - mean: {scaler.get('mean', 'N/A'):.3f}, scale: {scaler.get('scale', 'N/A'):.3f}")
    
    print("✅ Model check complete")
    
except Exception as e:
    print(f"❌ Error: {e}")
