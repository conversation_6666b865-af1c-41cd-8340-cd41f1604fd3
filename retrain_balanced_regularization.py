#!/usr/bin/env python3
"""
🎯 BALANCED REGULARIZATION RETRAINING
Retrain all player props models with moderate regularization to balance
over-prediction prevention with model learning capability
"""

import sys
import asyncio
from pathlib import Path

# Add project root to path
sys.path.append('.')

from src.neural_cortex.player_props_neural_pipeline import Player<PERSON>ropsNeuralPipeline, PlayerPropsConfig

print("🎯 BALANCED REGULARIZATION RETRAINING")
print("=" * 60)

def main():
    """Main execution"""
    
    # Balanced configuration - moderate regularization
    balanced_config = {
        'batch_size': 32,
        'learning_rate': 0.001,
        'epochs': 12,           # Moderate epochs
        'weight_decay': 0.005,  # Moderate regularization (5x increase from 0.001)
        'dropout_rate': 0.25,   # Moderate dropout (increased from 0.2)
        'early_stopping_patience': 5,
        'output_activation': 'linear',  # Keep linear activation
        'hidden_dim': 128,      # Restore original size
        'num_layers': 3,        # Restore original layers
        'use_batch_norm': True
    }
    
    print(f"🔧 BALANCED TRAINING CONFIGURATION:")
    print(f"   Weight Decay: {balanced_config['weight_decay']} (5x increase, moderate)")
    print(f"   Dropout Rate: {balanced_config['dropout_rate']} (moderate increase)")
    print(f"   Hidden Dim: {balanced_config['hidden_dim']} (restored)")
    print(f"   Num Layers: {balanced_config['num_layers']} (restored)")
    print(f"   Epochs: {balanced_config['epochs']}")
    
    prop_types = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
    
    successful_models = 0
    results = []
    
    for prop_type in prop_types:
        print(f"\n🔥 RETRAINING {prop_type.upper()} MODEL:")
        print("-" * 40)
        
        try:
            # Create balanced config
            config = PlayerPropsConfig(
                prop_type=prop_type,
                batch_size=balanced_config['batch_size'],
                learning_rate=balanced_config['learning_rate'],
                num_epochs=balanced_config['epochs'],
                weight_decay=balanced_config['weight_decay'],
                dropout_rate=balanced_config['dropout_rate'],
                early_stopping_patience=balanced_config['early_stopping_patience'],
                output_activation=balanced_config['output_activation'],
                hidden_dim=balanced_config['hidden_dim'],
                num_layers=balanced_config['num_layers'],
                use_batch_norm=balanced_config['use_batch_norm']
            )
            
            # Initialize pipeline
            pipeline = PlayerPropsNeuralPipeline(config)
            
            # Train model (async)
            result = asyncio.run(pipeline.train())
            
            if result and 'test_r2' in result:
                train_r2 = result.get('train_r2', 0)
                test_r2 = result.get('test_r2', 0)
                overfitting = train_r2 - test_r2
                
                print(f"   ✅ Training completed successfully!")
                print(f"   📊 Train R²: {train_r2:.4f}")
                print(f"   📊 Test R²:  {test_r2:.4f}")
                print(f"   📊 Overfitting: {overfitting:.4f}")
                
                # Assess quality
                if test_r2 > 0.3:
                    status = "✅ Good"
                elif test_r2 > 0.1:
                    status = "⚠️ Fair"
                else:
                    status = "❌ Poor"
                
                results.append({
                    'prop': prop_type,
                    'train_r2': train_r2,
                    'test_r2': test_r2,
                    'overfitting': overfitting,
                    'status': status
                })
                
                successful_models += 1
                
            else:
                print(f"   ❌ Training failed - no results returned")
                results.append({
                    'prop': prop_type,
                    'train_r2': 0.0,
                    'test_r2': 0.0,
                    'overfitting': 0.0,
                    'status': "❌ Failed"
                })
                
        except Exception as e:
            print(f"   ❌ Training error: {e}")
            results.append({
                'prop': prop_type,
                'train_r2': 0.0,
                'test_r2': 0.0,
                'overfitting': 0.0,
                'status': "❌ Error"
            })
    
    # Print summary
    print(f"\n" + "=" * 60)
    print(f"📋 BALANCED RETRAINING RESULTS SUMMARY:")
    print("=" * 60)
    print(f"✅ Successful: {successful_models}/{len(prop_types)} models")
    print()
    
    print("Prop     Train R² Test R²  Overfitting Status")
    print("-" * 50)
    for result in results:
        print(f"{result['prop']:<8} {result['train_r2']:<8.3f} {result['test_r2']:<8.3f} {result['overfitting']:<11.3f} {result['status']}")
    
    print(f"\n🎯 NEXT STEPS:")
    if successful_models == len(prop_types):
        avg_test_r2 = sum(r['test_r2'] for r in results) / len(results)
        if avg_test_r2 > 0.5:
            print(f"1. ✅ EXCELLENT: Models are learning well (avg R² = {avg_test_r2:.3f})")
            print(f"2. Test predictions to check if over-prediction is resolved")
        elif avg_test_r2 > 0.3:
            print(f"1. ✅ GOOD: Models are learning adequately (avg R² = {avg_test_r2:.3f})")
            print(f"2. Test predictions to check balance of realism vs diversity")
        else:
            print(f"1. ⚠️ FAIR: Models learning but could be better (avg R² = {avg_test_r2:.3f})")
            print(f"2. Consider reducing regularization further")
    else:
        print(f"1. ❌ Some models failed - check training pipeline")
        print(f"2. Review error messages and fix issues")

if __name__ == "__main__":
    main()
