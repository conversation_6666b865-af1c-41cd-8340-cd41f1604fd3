#!/usr/bin/env python3
"""
🔧 RETRAIN MODELS WITH OUTLIER REMOVAL AND INCREASED REGULARIZATION
Fix high-end over-prediction by removing extreme outliers and adding regularization
"""

import sys
import pandas as pd
import numpy as np
import asyncio
from pathlib import Path

# Add project root to path
sys.path.append('.')

from src.neural_cortex.player_props_neural_pipeline import PlayerPropsNeuralPipeline, PlayerPropsConfig

print("🔧 RETRAINING MODELS WITH OUTLIER REMOVAL")
print("=" * 60)

def remove_outliers_from_training_data():
    """Remove extreme outliers from training data files"""
    
    prop_types = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
    
    # Define realistic per-game caps based on analysis
    realistic_caps = {
        'points': 25.0,      # Cap at 25 instead of 28.5 (removes 1 outlier)
        'rebounds': 15.0,    # Cap at 15 instead of 20.1 (removes 1 outlier)
        'assists': 8.0,      # Cap at 8 instead of 9.5 (removes a few outliers)
        'steals': 2.0,       # <PERSON> at 2 instead of 2.4 (removes a few outliers)
        'blocks': 2.0,       # <PERSON> at 2 instead of 2.3 (removes a few outliers)
        'threes': 3.5        # Cap at 3.5 instead of 3.9 (removes 1 outlier)
    }
    
    for prop_type in prop_types:
        file_path = f"data/real_wnba_{prop_type}_training_data.csv"
        
        if not Path(file_path).exists():
            print(f"❌ File not found: {file_path}")
            continue
        
        print(f"\n🔧 Processing {prop_type} training data...")
        
        # Load data
        df = pd.read_csv(file_path)
        original_count = len(df)
        
        # Check if we have the target column
        target_col = f"{prop_type}_target" if f"{prop_type}_target" in df.columns else prop_type
        
        if target_col not in df.columns:
            print(f"❌ Target column '{target_col}' not found")
            continue
        
        # Filter for valid games (>= 5 games played)
        if 'games_played' in df.columns:
            valid_games = df['games_played'] >= 5
            df_filtered = df[valid_games].copy()
            
            # Calculate per-game values
            per_game_values = df_filtered[target_col] / df_filtered['games_played']
            
            # Remove outliers based on per-game caps
            cap = realistic_caps[prop_type]
            outlier_mask = per_game_values <= cap
            
            df_clean = df_filtered[outlier_mask].copy()
            
            removed_count = len(df_filtered) - len(df_clean)
            
            print(f"   📊 Original records: {original_count}")
            print(f"   📊 Valid games (>=5): {len(df_filtered)}")
            print(f"   📊 After outlier removal: {len(df_clean)}")
            print(f"   🗑️  Removed {removed_count} outliers (>{cap} per game)")
            
            if removed_count > 0:
                # Save cleaned data
                backup_path = file_path.replace('.csv', '_backup.csv')
                df.to_csv(backup_path, index=False)
                print(f"   💾 Backup saved: {backup_path}")
                
                df_clean.to_csv(file_path, index=False)
                print(f"   ✅ Cleaned data saved: {file_path}")
            else:
                print(f"   ✅ No outliers to remove")
        else:
            print(f"   ⚠️  No games_played column found")

def retrain_all_models_with_regularization():
    """Retrain all models with increased regularization"""
    
    prop_types = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
    
    # Enhanced configuration with more regularization
    enhanced_config = {
        'batch_size': 32,
        'learning_rate': 0.001,
        'epochs': 15,           # Slightly more epochs
        'weight_decay': 0.01,   # Increased from 0.001 to 0.01 (10x more regularization)
        'dropout_rate': 0.3,    # Increased from 0.2 to 0.3
        'early_stopping_patience': 5,
        'output_activation': 'linear',  # Keep linear activation
        'hidden_dim': 64,       # Smaller network (was 128)
        'num_layers': 2,        # Fewer layers (was 3)
        'use_batch_norm': True
    }
    
    print(f"\n🔧 ENHANCED TRAINING CONFIGURATION:")
    print(f"   Weight Decay: {enhanced_config['weight_decay']} (10x increase)")
    print(f"   Dropout Rate: {enhanced_config['dropout_rate']} (increased)")
    print(f"   Hidden Dim: {enhanced_config['hidden_dim']} (smaller)")
    print(f"   Num Layers: {enhanced_config['num_layers']} (fewer)")
    print(f"   Epochs: {enhanced_config['epochs']}")
    
    results = []
    
    for prop_type in prop_types:
        print(f"\n🔥 RETRAINING {prop_type.upper()} MODEL:")
        print("-" * 40)
        
        try:
            # Create enhanced config
            config = PlayerPropsConfig(
                prop_type=prop_type,
                batch_size=enhanced_config['batch_size'],
                learning_rate=enhanced_config['learning_rate'],
                num_epochs=enhanced_config['epochs'],  # Use num_epochs instead of epochs
                weight_decay=enhanced_config['weight_decay'],
                dropout_rate=enhanced_config['dropout_rate'],
                early_stopping_patience=enhanced_config['early_stopping_patience'],
                output_activation=enhanced_config['output_activation'],
                hidden_dim=enhanced_config['hidden_dim'],
                num_layers=enhanced_config['num_layers'],
                use_batch_norm=enhanced_config['use_batch_norm']
            )

            # Initialize pipeline
            pipeline = PlayerPropsNeuralPipeline(config=config, prop_type=prop_type)
            
            # Train model (async)
            result = asyncio.run(pipeline.train())
            
            if result and result.get('success', False):
                test_r2 = result.get('test_r2', 0)
                train_r2 = result.get('train_r2', 0)
                
                print(f"   ✅ Training completed successfully!")
                print(f"   📊 Train R²: {train_r2:.4f}")
                print(f"   📊 Test R²:  {test_r2:.4f}")
                print(f"   📊 Overfitting: {train_r2 - test_r2:.4f}")
                
                results.append({
                    'prop_type': prop_type,
                    'success': True,
                    'train_r2': train_r2,
                    'test_r2': test_r2,
                    'overfitting': train_r2 - test_r2
                })
            else:
                print(f"   ❌ Training failed")
                results.append({
                    'prop_type': prop_type,
                    'success': False,
                    'train_r2': 0,
                    'test_r2': 0,
                    'overfitting': 0
                })
                
        except Exception as e:
            print(f"   ❌ Training error: {e}")
            results.append({
                'prop_type': prop_type,
                'success': False,
                'train_r2': 0,
                'test_r2': 0,
                'overfitting': 0
            })
    
    return results

def main():
    """Main execution"""
    
    print(f"🎯 STEP 1: REMOVE EXTREME OUTLIERS FROM TRAINING DATA")
    remove_outliers_from_training_data()
    
    print(f"\n🎯 STEP 2: RETRAIN ALL MODELS WITH ENHANCED REGULARIZATION")
    results = retrain_all_models_with_regularization()
    
    # Summary
    print(f"\n" + "=" * 60)
    print(f"📋 RETRAINING RESULTS SUMMARY:")
    print("=" * 60)
    
    successful = [r for r in results if r['success']]
    
    print(f"✅ Successful: {len(successful)}/6 models")
    
    if successful:
        print(f"\n{'Prop':<8} {'Train R²':<8} {'Test R²':<8} {'Overfitting':<11} {'Status'}")
        print("-" * 50)
        
        for r in results:
            if r['success']:
                status = "✅ Good" if r['overfitting'] < 0.1 else "⚠️ Overfit"
                print(f"{r['prop_type']:<8} {r['train_r2']:<8.3f} {r['test_r2']:<8.3f} {r['overfitting']:<11.3f} {status}")
            else:
                print(f"{r['prop_type']:<8} {'FAILED':<8} {'FAILED':<8} {'FAILED':<11} ❌ Error")
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"1. Test the retrained models with realistic bounds")
    print(f"2. Verify predictions are now in realistic ranges")
    print(f"3. Check that diversity is maintained")

if __name__ == "__main__":
    main()
