#!/usr/bin/env python3
"""
🏀 RETRAIN ALL WNBA MODELS WITH NEW FORMAT
==========================================

This script retrains all WNBA player props models with the new format that includes:
- feature_list for proper feature alignment
- feature_scaler_params for proper scaling
- Complete checkpoint structure for inference

This will replace the legacy models with production-ready models.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import logging
import time
from datetime import datetime
from pathlib import Path

from src.neural_cortex.player_props_neural_pipeline import (
    PlayerPropsNeuralPipeline,
    PlayerPropsConfig
)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# All WNBA player props to train
WNBA_PLAYER_PROPS = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']

async def train_wnba_prop_model(prop_type: str) -> dict:
    """Train a single WNBA player prop model with new format"""
    logger.info(f"🏀 Training WNBA {prop_type} model with new format...")

    try:
        # Create optimized config for WNBA - use direct PlayerPropsConfig
        config = PlayerPropsConfig(
            league='WNBA',
            prop_type=prop_type,

            # Enhanced settings for production models
            num_epochs=100,  # More epochs for better training
            batch_size=64,   # Optimal batch size
            learning_rate=0.001,
            early_stopping_patience=15,
            weight_decay=0.01,
            dropout_rate=0.3,
            hidden_dim=128,
            num_layers=3,

            # Model architecture
            output_activation="linear",
            loss_function="mse",
            prediction_target="regression",

            # Feature engineering
            include_player_features=True,
            include_matchup_features=True,
            include_situational_features=True,
            include_recent_form=True,

            # Ensure proper output directory
            model_save_path="models/real_basketball_models"
        )
        
        # Create training pipeline
        pipeline = PlayerPropsNeuralPipeline(config, prop_type)
        
        # Train model
        start_time = time.time()
        results = await pipeline.train()
        training_time = time.time() - start_time
        
        if results and results.get('success', False):
            logger.info(f"✅ WNBA {prop_type} model training completed in {training_time:.1f}s!")
            logger.info(f"📊 Best validation loss: {results['best_val_loss']:.4f}")
            
            # Log detailed metrics if available
            if 'test_metrics' in results:
                metrics = results['test_metrics']
                logger.info(f"📊 Test MAE: {metrics.get('mae', 'N/A'):.4f}")
                logger.info(f"📊 Test R²: {metrics.get('r2', 'N/A'):.4f}")
                logger.info(f"📊 Accuracy (±1 unit): {metrics.get('accuracy_1pt', 'N/A'):.1f}%")
                logger.info(f"📊 Accuracy (±2 units): {metrics.get('accuracy_2pt', 'N/A'):.1f}%")
            
            return {
                'prop_type': prop_type,
                'status': 'success',
                'training_time': training_time,
                'results': results
            }
        else:
            logger.error(f"❌ WNBA {prop_type} model training failed!")
            return {
                'prop_type': prop_type,
                'status': 'failed',
                'training_time': training_time,
                'error': 'Training returned unsuccessful result'
            }
            
    except Exception as e:
        logger.error(f"💥 Error training WNBA {prop_type} model: {e}")
        return {
            'prop_type': prop_type,
            'status': 'error',
            'error': str(e)
        }

async def verify_model_format(model_path: str, prop_type: str) -> bool:
    """Verify that the trained model has the new format with feature_list and scaler_params"""
    try:
        import torch
        
        checkpoint = torch.load(model_path, map_location='cpu')
        
        # Check for required new format components
        has_feature_list = 'feature_list' in checkpoint
        has_scaler_params = 'feature_scaler_params' in checkpoint
        has_config = 'config' in checkpoint
        
        logger.info(f"🔍 Verifying {prop_type} model format:")
        logger.info(f"  ✅ feature_list: {'✓' if has_feature_list else '✗'}")
        logger.info(f"  ✅ feature_scaler_params: {'✓' if has_scaler_params else '✗'}")
        logger.info(f"  ✅ config: {'✓' if has_config else '✗'}")
        
        if has_feature_list:
            feature_count = len(checkpoint['feature_list'])
            logger.info(f"  📊 Features: {feature_count}")
        
        return has_feature_list and has_scaler_params and has_config
        
    except Exception as e:
        logger.error(f"❌ Error verifying {prop_type} model: {e}")
        return False

async def main():
    """Main training function for all WNBA models"""
    logger.info("🚀 STARTING WNBA MODEL RETRAINING WITH NEW FORMAT")
    logger.info("=" * 60)
    logger.info(f"📅 Training started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"🎯 Target models: {', '.join(WNBA_PLAYER_PROPS)}")
    logger.info(f"🏀 League: WNBA")
    logger.info(f"💾 Output directory: models/real_basketball_models/")
    logger.info("=" * 60)
    
    # Ensure output directory exists
    output_dir = Path("models/real_basketball_models")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Track training results
    successful_models = []
    failed_models = []
    total_start_time = time.time()
    
    # Train each player prop model
    for i, prop_type in enumerate(WNBA_PLAYER_PROPS, 1):
        logger.info(f"\n🔥 TRAINING MODEL {i}/{len(WNBA_PLAYER_PROPS)}: {prop_type.upper()}")
        logger.info("-" * 40)
        
        # Train the model
        result = await train_wnba_prop_model(prop_type)
        
        if result['status'] == 'success':
            successful_models.append(prop_type)
            
            # Verify the model has the new format
            model_path = f"models/real_basketball_models/real_{prop_type}_model.pt"
            if Path(model_path).exists():
                is_valid = await verify_model_format(model_path, prop_type)
                if is_valid:
                    logger.info(f"✅ {prop_type} model verified with new format!")
                else:
                    logger.warning(f"⚠️ {prop_type} model may not have complete new format")
            else:
                logger.warning(f"⚠️ {prop_type} model file not found at expected location")
        else:
            failed_models.append(prop_type)
            logger.error(f"❌ {prop_type} model training failed: {result.get('error', 'Unknown error')}")
    
    # Training summary
    total_time = time.time() - total_start_time
    logger.info("\n" + "=" * 60)
    logger.info("🏁 WNBA MODEL RETRAINING COMPLETED!")
    logger.info("=" * 60)
    logger.info(f"📅 Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"⏱️ Total time: {total_time:.1f} seconds")
    logger.info(f"✅ Successful models: {len(successful_models)}/{len(WNBA_PLAYER_PROPS)}")
    
    if successful_models:
        logger.info(f"🎯 Successfully trained: {', '.join(successful_models)}")
    
    if failed_models:
        logger.info(f"❌ Failed models: {', '.join(failed_models)}")
    else:
        logger.info("🎉 ALL WNBA MODELS TRAINED SUCCESSFULLY!")
        logger.info("🚀 Ready for production predictions with new format!")
    
    # Final verification
    logger.info("\n🔍 FINAL MODEL VERIFICATION:")
    logger.info("-" * 30)
    
    models_dir = Path("models/real_basketball_models")
    for prop_type in WNBA_PLAYER_PROPS:
        model_file = models_dir / f"real_{prop_type}_model.pt"
        if model_file.exists():
            logger.info(f"✅ {prop_type}: Model file exists")
        else:
            logger.info(f"❌ {prop_type}: Model file missing")
    
    logger.info("\n🎯 WNBA neural prediction system ready for testing!")

if __name__ == "__main__":
    asyncio.run(main())
