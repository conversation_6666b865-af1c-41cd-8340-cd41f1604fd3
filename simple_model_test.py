#!/usr/bin/env python3
"""
🔧 SIMPLE MODEL TEST: Check if the retrained models are working
"""

import sys
import torch
import numpy as np
from pathlib import Path

# Add project root to path
sys.path.append('.')

print("🔧 SIMPLE MODEL TEST")
print("=" * 40)

def test_model_loading():
    """Test if models can be loaded"""
    
    prop_types = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
    
    for prop_type in prop_types:
        model_path = f"models/player_props/nba_{prop_type}/best_{prop_type}_model.pt"
        
        print(f"\n🔍 Testing {prop_type} model...")
        
        if not Path(model_path).exists():
            print(f"   ❌ Model file not found: {model_path}")
            continue
        
        try:
            # Load the checkpoint
            checkpoint = torch.load(model_path, map_location='cpu')
            
            print(f"   ✅ Model loaded successfully")
            print(f"   📊 Keys in checkpoint: {list(checkpoint.keys())}")
            
            if 'model_state_dict' in checkpoint:
                print(f"   📊 Model state dict loaded")
            
            if 'target_scaler_params' in checkpoint:
                scaler_params = checkpoint['target_scaler_params']
                print(f"   📊 Target scaler: mean={scaler_params.get('mean', 'N/A'):.3f}, scale={scaler_params.get('scale', 'N/A'):.3f}")
            
            if 'config' in checkpoint:
                config = checkpoint['config']
                print(f"   📊 Config: epochs={config.get('num_epochs', 'N/A')}, lr={config.get('learning_rate', 'N/A')}")
            
            if 'metrics' in checkpoint:
                metrics = checkpoint['metrics']
                print(f"   📊 Performance: R²={metrics.get('test_r2', 'N/A'):.3f}")
            
        except Exception as e:
            print(f"   ❌ Error loading model: {e}")

def test_model_inference():
    """Test if models can make predictions"""
    
    print(f"\n🎯 TESTING MODEL INFERENCE")
    print("-" * 40)
    
    # Create dummy input (30 features as expected)
    dummy_input = torch.randn(1, 30)  # Batch size 1, 30 features
    
    prop_types = ['points', 'rebounds', 'assists']  # Test first 3
    
    for prop_type in prop_types:
        model_path = f"models/player_props/nba_{prop_type}/best_{prop_type}_model.pt"
        
        print(f"\n🔍 Testing {prop_type} inference...")
        
        if not Path(model_path).exists():
            print(f"   ❌ Model file not found")
            continue
        
        try:
            # Load checkpoint
            checkpoint = torch.load(model_path, map_location='cpu')
            
            # Recreate model architecture (simplified)
            from torch import nn
            
            class SimpleModel(nn.Module):
                def __init__(self):
                    super().__init__()
                    self.layers = nn.Sequential(
                        nn.Linear(30, 64),
                        nn.ReLU(),
                        nn.Dropout(0.3),
                        nn.Linear(64, 32),
                        nn.ReLU(),
                        nn.Dropout(0.3),
                        nn.Linear(32, 1)  # Linear output for regression
                    )
                
                def forward(self, x):
                    return self.layers(x)
            
            # Create model and load state
            model = SimpleModel()
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval()
            
            # Make prediction
            with torch.no_grad():
                raw_output = model(dummy_input)
                raw_value = raw_output.item()
            
            # Apply target unscaling if available
            if 'target_scaler_params' in checkpoint:
                scaler_params = checkpoint['target_scaler_params']
                mean = scaler_params.get('mean', 0)
                scale = scaler_params.get('scale', 1)
                unscaled_value = (raw_value * scale) + mean
            else:
                unscaled_value = raw_value
            
            print(f"   ✅ Inference successful")
            print(f"   📊 Raw output: {raw_value:.3f}")
            print(f"   📊 Unscaled output: {unscaled_value:.3f}")
            
            # Check if output is reasonable
            reasonable_ranges = {
                'points': (0, 30),
                'rebounds': (0, 20),
                'assists': (0, 15)
            }
            
            min_val, max_val = reasonable_ranges[prop_type]
            if min_val <= unscaled_value <= max_val:
                print(f"   ✅ Output in reasonable range ({min_val}-{max_val})")
            else:
                print(f"   ⚠️ Output outside reasonable range ({min_val}-{max_val})")
            
        except Exception as e:
            print(f"   ❌ Inference error: {e}")

def main():
    """Main execution"""
    
    print(f"🎯 Testing retrained models with enhanced regularization")
    
    # Test model loading
    test_model_loading()
    
    # Test model inference
    test_model_inference()
    
    print(f"\n🎯 SUMMARY:")
    print(f"1. If models load successfully: ✅ Training completed")
    print(f"2. If inference works: ✅ Models are functional")
    print(f"3. If outputs are reasonable: ✅ Over-prediction may be fixed")
    print(f"4. If R² scores are 0.000: ⚠️ Models need less aggressive regularization")

if __name__ == "__main__":
    main()
