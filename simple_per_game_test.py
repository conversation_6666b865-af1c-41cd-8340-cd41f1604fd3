#!/usr/bin/env python3
"""
Simple test to verify the per-game calculation fix
"""

import pandas as pd
import os

def test_per_game_calculation():
    """Test the per-game calculation logic directly"""
    print("🧪 Testing Per-Game Calculation Fix")
    print("=" * 50)
    
    # Load the real WNBA data
    data_path = "data/complete_real_wnba_features_with_metadata.csv"
    
    if not os.path.exists(data_path):
        print(f"❌ Data file not found: {data_path}")
        return
    
    # Load data
    data = pd.read_csv(data_path)
    print(f"✅ Loaded {len(data)} records")
    
    # Test for points
    prop_type = "points"
    
    print(f"\n🔧 Testing {prop_type} calculation...")
    
    # Apply the same logic as our fix
    # 1. Filter out players with < 5 games
    initial_count = len(data)
    filtered_data = data[data['games_played'] >= 5].copy()
    filtered_count = len(filtered_data)
    print(f"   Filtered out {initial_count - filtered_count} players with < 5 games")
    
    # 2. Check if values are season totals or per-game
    max_raw_value = filtered_data[prop_type].max()
    print(f"   Max raw {prop_type} value: {max_raw_value:.1f}")
    
    # 3. Convert if needed (points > 50 suggests season totals)
    if max_raw_value > 50:
        print(f"   Converting season totals to per-game...")
        filtered_data['per_game'] = filtered_data[prop_type] / filtered_data['games_played']
    else:
        print(f"   Using values as-is (already per-game)")
        filtered_data['per_game'] = filtered_data[prop_type]
    
    # 4. Clip to realistic ranges
    filtered_data['per_game'] = filtered_data['per_game'].clip(upper=35)
    print(f"   Clipped to maximum of 35 per game")
    
    # 5. Show results
    min_val = filtered_data['per_game'].min()
    max_val = filtered_data['per_game'].max()
    mean_val = filtered_data['per_game'].mean()
    median_val = filtered_data['per_game'].median()
    
    print(f"\n📈 FINAL RESULTS:")
    print(f"   Records: {len(filtered_data)}")
    print(f"   Min per-game: {min_val:.2f}")
    print(f"   Max per-game: {max_val:.2f}")
    print(f"   Mean per-game: {mean_val:.2f}")
    print(f"   Median per-game: {median_val:.2f}")
    
    # Check if realistic
    if max_val <= 35:
        print(f"   ✅ REALISTIC RANGE - Max {max_val:.1f} is within expected WNBA range")
    else:
        print(f"   ❌ UNREALISTIC RANGE - Max {max_val:.1f} is too high")
    
    # Show top performers
    print(f"\n🏆 Top 10 {prop_type} per game:")
    top_performers = filtered_data.nlargest(10, 'per_game')[['player_name', 'per_game', 'games_played', prop_type]]
    for _, row in top_performers.iterrows():
        print(f"   {row['player_name']}: {row['per_game']:.1f} per game ({row['games_played']} games, {row[prop_type]:.1f} total)")

if __name__ == "__main__":
    test_per_game_calculation()
