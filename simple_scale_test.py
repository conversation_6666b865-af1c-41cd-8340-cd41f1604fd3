#!/usr/bin/env python3
"""
Simple test to verify the scale fix is working
"""

import sys
import os
import logging
import asyncio

# Add project root to path
sys.path.insert(0, os.path.abspath('.'))

# Set up basic logging
logging.basicConfig(level=logging.INFO)

async def test_scale_fix():
    try:
        print("🔍 Testing scale fix...")

        # Import the service
        from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
        print("✅ Import successful")

        # Initialize service
        service = UnifiedNeuralPredictionService()
        print("✅ Service initialized")

        # Create test game data
        game_data = {
            "home_team": "Las Vegas Aces",
            "away_team": "Connecticut Sun",
            "game_date": "2025-07-06",
            "league": "WNBA"
        }

        # Test players with different characteristics
        test_players = [
            {"name": "<PERSON>'ja <PERSON>", "team": "Las Vegas Aces", "position": "F"},
            {"name": "<PERSON> Plum", "team": "Las Vegas Aces", "position": "<PERSON>"},
            {"name": "<PERSON><PERSON><PERSON>", "team": "Connecticut Sun", "position": "F"},
            {"name": "<PERSON><PERSON>", "team": "Connecticut Sun", "position": "F"}
        ]

        print(f"🏀 Testing unified prediction with {len(test_players)} players...")

        # Get unified predictions
        result = await service.predict_unified(game_data, test_players)

        print("📊 Game Prediction Results:")
        if hasattr(result, 'game_predictions') and result.game_predictions:
            for key, value in result.game_predictions.items():
                print(f"  {key}: {value}")

        print("\n📊 Player Props Results:")
        if hasattr(result, 'player_props') and result.player_props:
            all_predictions = []

            for player_name, props in result.player_props.items():
                print(f"\n👤 {player_name}:")
                for prop_type, prediction in props.items():
                    print(f"  {prop_type:8}: {prediction:5.1f}")
                    all_predictions.append(prediction)

            # Analyze diversity
            unique_values = len(set(all_predictions))
            print(f"\n🔍 Analysis:")
            print(f"  Total predictions: {len(all_predictions)}")
            print(f"  Unique values: {unique_values}")
            print(f"  Range: {min(all_predictions):.1f} - {max(all_predictions):.1f}")

            if unique_values > 1:
                print("✅ SUCCESS: Predictions are diverse!")
            else:
                print("❌ FAILED: All predictions are identical!")

            if all(0 <= v <= 50 for v in all_predictions):
                print("✅ SUCCESS: All predictions are in realistic ranges!")
            else:
                print("⚠️ Some predictions may be unrealistic")
        else:
            print("❌ No player props found in result")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_scale_fix())
