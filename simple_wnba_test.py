#!/usr/bin/env python3
"""
Simple test script to verify WNBA neural prediction system is working correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
import asyncio

async def test_wnba_predictions():
    """Test WNBA predictions with real player data."""
    print("🏀 Testing WNBA Neural Prediction System...")
    
    # Initialize the service
    service = UnifiedNeuralPredictionService()
    
    # Test data from the WNBA boxscore - A'ja <PERSON> (star player)
    test_player_data = {
        'player_id': 1628932,
        'player_name': "A'ja <PERSON>",
        'team_id': 1611661319,
        'team_abbreviation': 'LVA',
        'season_avg_pts': 22.8,
        'season_avg_reb': 11.9,
        'season_avg_ast': 2.3,
        'season_avg_stl': 1.8,
        'season_avg_blk': 2.6,
        'season_avg_fg3m': 0.4,
        'games_played': 38,
        'minutes_per_game': 34.6,
        'usage_rate': 28.5,
        'true_shooting_pct': 0.578,
        'effective_fg_pct': 0.521,
        'league': 'WNBA'
    }
    
    # Game data for unified prediction
    game_data = {
        'home_team_id': 1611661319,
        'away_team_id': 1611661314,
        'home_team_abbreviation': 'LVA',
        'away_team_abbreviation': 'CHI',
        'league': 'WNBA'
    }
    
    print(f"\n📊 Testing predictions for A'ja Wilson:")
    
    try:
        # Use the unified prediction method
        result = await service.predict_unified(game_data, [test_player_data])
        
        print(f"✅ Prediction completed successfully!")
        print(f"Result type: {type(result)}")
        
        # Check if we have game prediction
        if hasattr(result, 'game_prediction'):
            print(f"Game prediction: {result.game_prediction}")
        
        # Check if we have player props
        if hasattr(result, 'player_props') and result.player_props:
            print(f"Player props found for {len(result.player_props)} players")
            
            player_props = result.player_props[0]  # First player
            print(f"Player props: {player_props}")
            
            # Check each prop
            for prop_name, prop_data in player_props.items():
                if isinstance(prop_data, dict):
                    value = prop_data.get('prediction', 'N/A')
                    confidence = prop_data.get('confidence', 'N/A')
                    
                    print(f"  {prop_name.capitalize()}: {value} (confidence: {confidence})")
                    
                    # Validate prediction is reasonable for WNBA
                    if isinstance(value, (int, float)):
                        if prop_name == 'points' and not (0 <= value <= 40):
                            print(f"    ⚠️  WARNING: {prop_name} prediction {value} seems unrealistic for WNBA")
                        elif prop_name == 'rebounds' and not (0 <= value <= 20):
                            print(f"    ⚠️  WARNING: {prop_name} prediction {value} seems unrealistic for WNBA")
                        elif prop_name == 'assists' and not (0 <= value <= 15):
                            print(f"    ⚠️  WARNING: {prop_name} prediction {value} seems unrealistic for WNBA")
                        elif prop_name in ['steals', 'blocks', 'threes'] and not (0 <= value <= 10):
                            print(f"    ⚠️  WARNING: {prop_name} prediction {value} seems unrealistic for WNBA")
                        else:
                            print(f"    ✅ {prop_name} prediction {value} looks reasonable for WNBA")
        else:
            print("  ❌ No player props found in result")
            
    except Exception as e:
        print(f"❌ Error testing prediction: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """Run the WNBA prediction test."""
    print("🚀 Starting Simple WNBA Neural Prediction Test...")
    print("=" * 60)
    
    # Run the async test
    asyncio.run(test_wnba_predictions())
    
    print("\n" + "=" * 60)
    print("✅ WNBA prediction test completed!")

if __name__ == "__main__":
    main()
