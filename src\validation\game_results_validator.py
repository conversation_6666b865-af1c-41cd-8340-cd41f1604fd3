"""
Game Results Validator - HYPER MEDUSA NEURAL VAULT
==================================================

This module validates our predictions against actual game results to improve
the neural prediction system. It incorporates real game outcomes and player
statistics to enhance model accuracy.

Author: HYPER MEDUSA NEURAL VAULT
Version: 1.0.0
"""

import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json
import os
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class GameResult:
    """Represents actual game results for validation"""
    game_id: str
    date: str
    home_team: str
    away_team: str
    home_score: int
    away_score: int
    winner: str
    final_spread: float
    final_total: int
    league: str = "WNBA"

@dataclass
class PlayerResult:
    """Represents actual player performance for validation"""
    player_name: str
    team: str
    points: float
    rebounds: float
    assists: float
    steals: float
    blocks: float
    threes: float
    minutes: float

@dataclass
class ValidationResult:
    """Results of prediction validation"""
    game_id: str
    prediction_accuracy: float
    spread_accuracy: float
    total_accuracy: float
    player_props_accuracy: Dict[str, float]
    confidence_calibration: float
    errors: List[str]

class GameResultsValidator:
    """
    Validates predictions against actual game results and provides
    feedback for neural model improvement.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.results_cache = {}
        self.validation_history = []
        
        # Create validation data directory
        self.validation_dir = "data/validation"
        os.makedirs(self.validation_dir, exist_ok=True)
        
        self.logger.info("🔍 Game Results Validator initialized")
    
    def add_game_result(self, game_result: GameResult, player_results: List[PlayerResult] = None) -> bool:
        """
        Add actual game results for validation
        
        Args:
            game_result: Actual game outcome
            player_results: List of actual player performances
            
        Returns:
            bool: Success status
        """
        try:
            # Store game result
            game_key = f"{game_result.date}_{game_result.home_team}_{game_result.away_team}"
            
            self.results_cache[game_key] = {
                'game': game_result,
                'players': player_results or [],
                'timestamp': datetime.now().isoformat()
            }
            
            # Save to file for persistence
            self._save_result_to_file(game_key, self.results_cache[game_key])
            
            self.logger.info(f"✅ Added game result: {game_result.home_team} {game_result.home_score} - {game_result.away_score} {game_result.away_team}")
            
            if player_results:
                self.logger.info(f"📊 Added {len(player_results)} player results")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to add game result: {e}")
            return False
    
    def validate_prediction(self, game_prediction: Dict[str, Any], 
                          player_predictions: Dict[str, Dict[str, float]],
                          game_key: str) -> Optional[ValidationResult]:
        """
        Validate predictions against actual results
        
        Args:
            game_prediction: Our game prediction
            player_predictions: Our player prop predictions
            game_key: Key to find actual results
            
        Returns:
            ValidationResult or None if no actual results found
        """
        try:
            if game_key not in self.results_cache:
                self.logger.warning(f"⚠️ No actual results found for {game_key}")
                return None
            
            actual = self.results_cache[game_key]
            game_result = actual['game']
            player_results = actual['players']
            
            # Validate game prediction
            predicted_winner = game_prediction.get('winner', '')
            actual_winner = game_result.winner
            game_accuracy = 1.0 if predicted_winner == actual_winner else 0.0
            
            # Validate spread prediction
            predicted_spread = game_prediction.get('predicted_spread', 0.0)
            actual_spread = game_result.final_spread
            spread_error = abs(predicted_spread - actual_spread)
            spread_accuracy = max(0.0, 1.0 - (spread_error / 10.0))  # Normalize by 10 points
            
            # Validate total prediction
            predicted_total = game_prediction.get('predicted_total', 0.0)
            actual_total = game_result.final_total
            total_error = abs(predicted_total - actual_total)
            total_accuracy = max(0.0, 1.0 - (total_error / 20.0))  # Normalize by 20 points
            
            # Validate player props
            player_props_accuracy = {}
            for player_name, predictions in player_predictions.items():
                # Find matching actual player result
                actual_player = next((p for p in player_results if p.player_name == player_name), None)
                
                if actual_player:
                    prop_accuracies = {}
                    for prop, predicted_value in predictions.items():
                        if hasattr(actual_player, prop):
                            actual_value = getattr(actual_player, prop)
                            error = abs(predicted_value - actual_value)
                            # Normalize error based on typical ranges for each stat
                            if prop == 'points':
                                accuracy = max(0.0, 1.0 - (error / 10.0))
                            elif prop in ['rebounds', 'assists']:
                                accuracy = max(0.0, 1.0 - (error / 5.0))
                            elif prop in ['steals', 'blocks', 'threes']:
                                accuracy = max(0.0, 1.0 - (error / 3.0))
                            else:
                                accuracy = max(0.0, 1.0 - (error / 5.0))
                            
                            prop_accuracies[prop] = accuracy
                    
                    player_props_accuracy[player_name] = prop_accuracies
            
            # Calculate confidence calibration
            predicted_confidence = game_prediction.get('confidence', 0.5)
            confidence_calibration = self._calculate_confidence_calibration(
                predicted_confidence, game_accuracy
            )
            
            validation_result = ValidationResult(
                game_id=game_key,
                prediction_accuracy=game_accuracy,
                spread_accuracy=spread_accuracy,
                total_accuracy=total_accuracy,
                player_props_accuracy=player_props_accuracy,
                confidence_calibration=confidence_calibration,
                errors=[]
            )
            
            # Store validation result
            self.validation_history.append(validation_result)
            self._save_validation_result(validation_result)
            
            self.logger.info(f"✅ Validation complete for {game_key}")
            self.logger.info(f"📊 Game accuracy: {game_accuracy:.1%}, Spread: {spread_accuracy:.1%}, Total: {total_accuracy:.1%}")
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"❌ Validation failed for {game_key}: {e}")
            return None
    
    def get_validation_summary(self, days_back: int = 7) -> Dict[str, Any]:
        """
        Get summary of validation results for recent predictions
        
        Args:
            days_back: Number of days to look back
            
        Returns:
            Summary statistics
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_back)
            recent_validations = [
                v for v in self.validation_history 
                if datetime.fromisoformat(v.game_id.split('_')[0]) >= cutoff_date
            ]
            
            if not recent_validations:
                return {"message": "No recent validations found"}
            
            # Calculate averages
            avg_game_accuracy = sum(v.prediction_accuracy for v in recent_validations) / len(recent_validations)
            avg_spread_accuracy = sum(v.spread_accuracy for v in recent_validations) / len(recent_validations)
            avg_total_accuracy = sum(v.total_accuracy for v in recent_validations) / len(recent_validations)
            
            # Player props accuracy
            all_player_accuracies = []
            for v in recent_validations:
                for player, props in v.player_props_accuracy.items():
                    all_player_accuracies.extend(props.values())
            
            avg_player_accuracy = sum(all_player_accuracies) / len(all_player_accuracies) if all_player_accuracies else 0.0
            
            summary = {
                "validation_period_days": days_back,
                "total_games_validated": len(recent_validations),
                "average_game_accuracy": avg_game_accuracy,
                "average_spread_accuracy": avg_spread_accuracy,
                "average_total_accuracy": avg_total_accuracy,
                "average_player_props_accuracy": avg_player_accuracy,
                "overall_system_accuracy": (avg_game_accuracy + avg_spread_accuracy + avg_total_accuracy + avg_player_accuracy) / 4,
                "recommendations": self._generate_improvement_recommendations(recent_validations)
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"❌ Failed to generate validation summary: {e}")
            return {"error": str(e)}
    
    def _calculate_confidence_calibration(self, predicted_confidence: float, actual_accuracy: float) -> float:
        """Calculate how well-calibrated our confidence scores are"""
        return 1.0 - abs(predicted_confidence - actual_accuracy)
    
    def _generate_improvement_recommendations(self, validations: List[ValidationResult]) -> List[str]:
        """Generate recommendations for improving the prediction system"""
        recommendations = []
        
        # Analyze common issues
        game_accuracies = [v.prediction_accuracy for v in validations]
        spread_accuracies = [v.spread_accuracy for v in validations]
        total_accuracies = [v.total_accuracy for v in validations]
        
        avg_game = sum(game_accuracies) / len(game_accuracies)
        avg_spread = sum(spread_accuracies) / len(spread_accuracies)
        avg_total = sum(total_accuracies) / len(total_accuracies)
        
        if avg_game < 0.6:
            recommendations.append("Game outcome predictions need improvement - consider adding more team form data")
        
        if avg_spread < 0.7:
            recommendations.append("Spread predictions are off - review point differential modeling")
        
        if avg_total < 0.7:
            recommendations.append("Total predictions need work - analyze pace and efficiency factors")
        
        return recommendations
    
    def _save_result_to_file(self, game_key: str, result_data: Dict[str, Any]):
        """Save game result to file for persistence"""
        try:
            filename = os.path.join(self.validation_dir, f"game_result_{game_key}.json")
            
            # Convert dataclasses to dict for JSON serialization
            serializable_data = {
                'game': result_data['game'].__dict__,
                'players': [p.__dict__ for p in result_data['players']],
                'timestamp': result_data['timestamp']
            }
            
            with open(filename, 'w') as f:
                json.dump(serializable_data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"❌ Failed to save result to file: {e}")
    
    def _save_validation_result(self, validation: ValidationResult):
        """Save validation result to file"""
        try:
            filename = os.path.join(self.validation_dir, f"validation_{validation.game_id}.json")
            
            with open(filename, 'w') as f:
                json.dump(validation.__dict__, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"❌ Failed to save validation result: {e}")
