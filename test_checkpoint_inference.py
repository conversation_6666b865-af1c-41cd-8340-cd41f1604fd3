#!/usr/bin/env python3
"""
🎯 TEST CHECKPOINT INFERENCE DIRECTLY
=====================================

Test the fixed checkpoint inference method directly.
"""

import torch
import pandas as pd
import numpy as np
import sys
sys.path.append('.')

def test_checkpoint_inference():
    """Test direct checkpoint inference"""
    
    print("🎯 TESTING CHECKPOINT INFERENCE")
    print("=" * 50)
    
    # Test one model
    model_path = "models/real_basketball_models/best_points_model.pt"
    
    print(f"📁 Testing model: {model_path}")
    
    try:
        # Load checkpoint to check contents
        checkpoint = torch.load(model_path, map_location='cpu')
        
        print(f"✅ Checkpoint loaded")
        print(f"📊 Keys: {list(checkpoint.keys())}")
        
        if 'feature_list' in checkpoint:
            feature_count = len(checkpoint['feature_list'])
            print(f"✅ Feature list found: {feature_count} features")
            print(f"🔍 Features: {checkpoint['feature_list'][:5]}...")  # Show first 5
        else:
            print(f"❌ No feature_list in checkpoint")
            return
        
        # Create test input DataFrame
        feature_list = checkpoint['feature_list']
        
        # Create sample data matching the feature list
        test_data = {}
        for feature in feature_list:
            # Generate realistic test values
            if 'stat_value' in feature:
                test_data[feature] = np.random.uniform(0, 30)  # Points range
            elif 'rank' in feature:
                test_data[feature] = np.random.uniform(0, 100)
            else:
                test_data[feature] = np.random.uniform(-2, 2)  # Normalized features
        
        input_df = pd.DataFrame([test_data])
        print(f"✅ Created test input: {input_df.shape}")
        
        # Test the inference method
        from src.neural_cortex.player_props_neural_pipeline import PlayerPropsTrainingPipeline
        
        print(f"🧠 Running inference...")
        
        result_df = PlayerPropsTrainingPipeline.predict_from_checkpoint(
            checkpoint_path=model_path,
            input_df=input_df,
            device='cpu',
            return_confidence=True
        )
        
        print(f"✅ Inference successful!")
        print(f"📊 Result shape: {result_df.shape}")
        print(f"📊 Columns: {list(result_df.columns)}")
        
        if 'prediction' in result_df.columns:
            prediction = result_df['prediction'].iloc[0]
            print(f"🎯 Prediction: {prediction:.2f}")
            
            # Check if prediction is realistic for points
            if 0 <= prediction <= 50:
                print(f"✅ Realistic prediction range")
            else:
                print(f"⚠️ Prediction outside expected range")
        
        if 'confidence' in result_df.columns:
            confidence = result_df['confidence'].iloc[0]
            print(f"📊 Confidence: {confidence:.3f}")
        
        print(f"\n🎉 CHECKPOINT INFERENCE TEST: SUCCESS!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_checkpoint_inference()
