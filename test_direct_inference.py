#!/usr/bin/env python3
"""
🎯 TEST DIRECT INFERENCE
========================

This script directly tests the PlayerPropsTrainingPipeline.predict_from_checkpoint
method to verify that the feature list fix works and produces diverse predictions.
"""

import asyncio
import pandas as pd
import numpy as np
from pathlib import Path

async def test_direct_inference():
    """Test direct inference with the fixed models"""
    
    print("🎯 TESTING DIRECT INFERENCE WITH FIXED MODELS")
    print("=" * 60)
    
    # Import the pipeline
    from src.neural_cortex.player_props_training_pipeline import PlayerPropsTrainingPipeline
    
    # Create test data for different players
    test_data = pd.DataFrame([
        {
            'player_name': 'A\'ja <PERSON>',
            'team': 'LAS',
            'position': 'F',
            'minutes_per_game': 35.0,
            'games_played': 30,
            'points': 25.0,
            'rebounds': 10.0,
            'assists': 3.0,
            'steals': 1.5,
            'blocks': 2.0,
            'threes': 1.0,
            'field_goals_made': 9.0,
            'field_goals_attempted': 16.0,
            'free_throws_made': 5.0,
            'free_throws_attempted': 6.0,
            'turnovers': 2.5,
            'personal_fouls': 2.0,
            'plus_minus': 8.5,
            'usage_rate': 28.0,
            'true_shooting_percentage': 0.62,
            'effective_field_goal_percentage': 0.58,
            'player_efficiency_rating': 25.0,
            'win_shares': 0.15,
            'box_plus_minus': 6.2,
            'value_over_replacement_player': 2.8,
            'offensive_rating': 115,
            'defensive_rating': 105,
            'net_rating': 10,
            'pace': 98.5,
            'tier': 1,
            'is_starter': 1
        },
        {
            'player_name': 'Sabrina Ionescu',
            'team': 'NY',
            'position': 'G',
            'minutes_per_game': 30.0,
            'games_played': 25,
            'points': 18.0,
            'rebounds': 4.0,
            'assists': 8.0,
            'steals': 1.2,
            'blocks': 0.2,
            'threes': 3.0,
            'field_goals_made': 6.5,
            'field_goals_attempted': 15.0,
            'free_throws_made': 2.0,
            'free_throws_attempted': 2.5,
            'turnovers': 3.0,
            'personal_fouls': 1.8,
            'plus_minus': 5.2,
            'usage_rate': 22.0,
            'true_shooting_percentage': 0.58,
            'effective_field_goal_percentage': 0.55,
            'player_efficiency_rating': 20.0,
            'win_shares': 0.12,
            'box_plus_minus': 4.1,
            'value_over_replacement_player': 1.8,
            'offensive_rating': 112,
            'defensive_rating': 108,
            'net_rating': 4,
            'pace': 96.0,
            'tier': 2,
            'is_starter': 1
        },
        {
            'player_name': 'Bench Player',
            'team': 'CHI',
            'position': 'G',
            'minutes_per_game': 15.0,
            'games_played': 20,
            'points': 8.0,
            'rebounds': 2.0,
            'assists': 3.0,
            'steals': 0.5,
            'blocks': 0.1,
            'threes': 1.0,
            'field_goals_made': 3.0,
            'field_goals_attempted': 7.0,
            'free_throws_made': 1.0,
            'free_throws_attempted': 1.2,
            'turnovers': 1.5,
            'personal_fouls': 1.2,
            'plus_minus': -2.0,
            'usage_rate': 15.0,
            'true_shooting_percentage': 0.48,
            'effective_field_goal_percentage': 0.45,
            'player_efficiency_rating': 12.0,
            'win_shares': 0.05,
            'box_plus_minus': -1.5,
            'value_over_replacement_player': 0.2,
            'offensive_rating': 105,
            'defensive_rating': 112,
            'net_rating': -7,
            'pace': 94.0,
            'tier': 3,
            'is_starter': 0
        }
    ])
    
    # Test each prop type
    prop_types = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
    
    all_predictions = []
    
    for prop_type in prop_types:
        print(f"\n🏀 Testing {prop_type} model...")
        
        model_path = f"models/real_basketball_models/best_{prop_type}_model.pt"
        
        if not Path(model_path).exists():
            print(f"   ❌ Model not found: {model_path}")
            continue
        
        try:
            # Get predictions for all players
            predictions = await PlayerPropsTrainingPipeline.predict_from_checkpoint(
                checkpoint_path=model_path,
                input_data=test_data,
                league='WNBA'
            )
            
            if predictions is not None and len(predictions) > 0:
                print(f"   ✅ Got {len(predictions)} predictions")
                
                for i, (_, player) in enumerate(test_data.iterrows()):
                    if i < len(predictions):
                        pred_value = predictions[i]
                        print(f"      {player['player_name']:15}: {pred_value:6.1f}")
                        
                        all_predictions.append({
                            'player': player['player_name'],
                            'prop_type': prop_type,
                            'prediction': pred_value,
                            'actual_season_avg': player[prop_type]
                        })
                
                # Check diversity for this prop type
                pred_array = np.array(predictions[:len(test_data)])
                min_val = pred_array.min()
                max_val = pred_array.max()
                std_val = pred_array.std()
                
                print(f"   📊 Range: {min_val:.1f} - {max_val:.1f}, Std: {std_val:.2f}")
                
                if std_val < 0.01:
                    print(f"   ⚠️  WARNING: All predictions are identical!")
                else:
                    print(f"   ✅ Good diversity in predictions")
                    
            else:
                print(f"   ❌ No predictions returned")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            import traceback
            traceback.print_exc()
    
    # Overall analysis
    print("\n" + "=" * 60)
    print("📊 OVERALL ANALYSIS")
    print("=" * 60)
    
    if all_predictions:
        df = pd.DataFrame(all_predictions)
        
        print("🎯 Prediction Summary:")
        for prop_type in prop_types:
            prop_data = df[df['prop_type'] == prop_type]
            if len(prop_data) > 0:
                predictions = prop_data['prediction'].values
                min_val = predictions.min()
                max_val = predictions.max()
                std_val = predictions.std()
                
                print(f"  {prop_type:8}: {min_val:5.1f} - {max_val:5.1f} (std: {std_val:5.2f})")
        
        # Check if fix worked
        total_unique = len(df['prediction'].unique())
        total_predictions = len(df)
        
        print(f"\n🔍 Diversity Check:")
        print(f"   Total predictions: {total_predictions}")
        print(f"   Unique predictions: {total_unique}")
        print(f"   Diversity ratio: {total_unique/total_predictions:.2f}")
        
        if total_unique == 1:
            print("\n❌ CRITICAL: All predictions are identical (bug still present)")
        elif total_unique < total_predictions * 0.3:
            print("\n⚠️  WARNING: Low prediction diversity")
        else:
            print("\n🎉 EXCELLENT: Feature list fix successful!")
            print("✅ Models are producing diverse, realistic predictions")
            print("💯 Ready for production use")
    
    else:
        print("❌ No predictions to analyze")

if __name__ == "__main__":
    asyncio.run(test_direct_inference())
