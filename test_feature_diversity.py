#!/usr/bin/env python3
"""
Test feature diversity for a few players to identify the root cause
"""

import sys
import os
import asyncio
import logging

# Add project root to path
sys.path.insert(0, os.path.abspath('.'))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_feature_diversity():
    """Test feature diversity for a few players"""
    try:
        print("🔍 TESTING FEATURE DIVERSITY")
        print("=" * 50)
        
        # Import services
        from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
        
        # Initialize service
        service = UnifiedNeuralPredictionService()
        await service._load_player_props_models()
        print(f"✅ Loaded {len(service.player_props_models)} player props models")
        
        # Game data
        game_data = {
            "home_team": "Minnesota Lynx",
            "away_team": "Golden State Valkyries", 
            "game_date": "2025-07-05",
            "league": "WNBA"
        }
        
        # Test just 3 players with different characteristics
        test_players = [
            {"name": "<PERSON>", "team": "Golden State Valkyries", "position": "G"},
            {"name": "<PERSON><PERSON><PERSON><PERSON> Collier", "team": "Minnesota Lynx", "position": "F"},
            {"name": "<PERSON> <PERSON>", "team": "Minnesota Lynx", "position": "C"}
        ]
        
        print(f"\n🧠 Testing feature generation for {len(test_players)} players...")
        print("Looking for feature diversity issues...")
        
        # Test just points prediction to see feature differences
        prop_type = "points"
        
        for player in test_players:
            print(f"\n{'='*60}")
            print(f"🏀 PLAYER: {player['name']} ({player['position']}) - {player['team']}")
            print(f"{'='*60}")
            
            # Call the feature preparation method directly
            features = service._prepare_player_features(player, game_data, prop_type)
            
            if features is not None:
                print(f"✅ Generated {len(features)} features")
                print(f"📊 Feature summary:")
                print(f"   Range: {features.min():.2f} to {features.max():.2f}")
                print(f"   Unique values: {len(np.unique(features))}/30")
                print(f"   Mean: {features.mean():.2f}")
                print(f"   Std: {features.std():.2f}")
            else:
                print("❌ Failed to generate features")
        
        print(f"\n{'='*60}")
        print("🎯 FEATURE DIVERSITY ANALYSIS COMPLETE")
        print("Check the logs above for detailed feature breakdowns")
        print("Look for patterns in identical values across players")
        print(f"{'='*60}")

    except Exception as e:
        print(f"❌ Error during feature diversity test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import numpy as np
    asyncio.run(test_feature_diversity())
