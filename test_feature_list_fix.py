#!/usr/bin/env python3
"""
🔧 TEST FEATURE LIST FIX
========================

This script tests that the feature list fix works by retraining one WNBA model
and verifying that the feature_list is properly saved in the checkpoint.
"""

import asyncio
import torch
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_feature_list_fix():
    """Test that feature list is properly saved in checkpoint"""
    
    print("🔧 TESTING FEATURE LIST FIX")
    print("=" * 50)
    
    # Import the pipeline
    from src.neural_cortex.player_props_neural_pipeline import PlayerPropsNeuralPipeline, PlayerPropsConfig
    
    # Create config for points model (quick test)
    config = PlayerPropsConfig(
        prop_type="points",
        league="WNBA",
        model_save_path="models/test_feature_fix",
        num_epochs=3,  # Just 3 epochs for quick test
        batch_size=64,
        learning_rate=0.001,
        hidden_dim=128,
        num_layers=3,
        dropout_rate=0.2,
        early_stopping_patience=10,
        output_activation="linear"
    )
    
    # Create pipeline
    pipeline = PlayerPropsNeuralPipeline(config)
    
    print(f"🎯 Testing {config.prop_type} model training with feature list fix...")
    
    # Train the model
    try:
        results = await pipeline.train()
        print(f"✅ Training completed successfully!")
        print(f"📊 Final validation loss: {results.get('final_val_loss', 'N/A')}")
        
        # Check if model was saved
        model_path = Path(config.model_save_path) / f"best_{config.prop_type}_model.pt"
        if model_path.exists():
            print(f"✅ Model saved at: {model_path}")
            
            # Load and verify checkpoint has feature_list
            print("\n🔍 VERIFYING CHECKPOINT CONTENTS")
            print("-" * 30)
            
            checkpoint = torch.load(model_path, map_location='cpu')
            
            # Check for required components
            has_feature_list = 'feature_list' in checkpoint
            has_scaler_params = 'feature_scaler_params' in checkpoint
            has_config = 'config' in checkpoint
            has_model_state = 'model_state_dict' in checkpoint
            
            print(f"✅ feature_list: {'✓' if has_feature_list else '✗'}")
            print(f"✅ feature_scaler_params: {'✓' if has_scaler_params else '✗'}")
            print(f"✅ config: {'✓' if has_config else '✗'}")
            print(f"✅ model_state_dict: {'✓' if has_model_state else '✗'}")
            
            if has_feature_list:
                feature_list = checkpoint['feature_list']
                print(f"📊 Feature count: {len(feature_list)}")
                print(f"📋 First 5 features: {feature_list[:5]}")
                print(f"🎉 FEATURE LIST FIX SUCCESSFUL!")
                return True
            else:
                print(f"❌ FEATURE LIST STILL MISSING!")
                return False
        else:
            print(f"❌ Model file not found at {model_path}")
            return False
            
    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    success = await test_feature_list_fix()
    
    if success:
        print("\n🎉 FEATURE LIST FIX VERIFICATION SUCCESSFUL!")
        print("✅ New models will have proper feature_list for inference")
        print("🚀 Ready to retrain all WNBA models with the fix")
    else:
        print("\n❌ FEATURE LIST FIX VERIFICATION FAILED!")
        print("⚠️ Need to investigate further")

if __name__ == "__main__":
    asyncio.run(main())
