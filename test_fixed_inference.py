#!/usr/bin/env python3
"""
🧪 TEST FIXED INFERENCE
=======================

This script tests that the fixed WNBA models now produce diverse, realistic
predictions instead of constant maximum values for all players.
"""

import asyncio
import pandas as pd
from pathlib import Path

async def test_fixed_inference():
    """Test that the fixed models produce diverse predictions"""
    
    print("🧪 TESTING FIXED INFERENCE")
    print("=" * 50)
    
    # Import the service
    from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
    
    # Create test players with different stats
    test_players = [
        {
            'player_name': 'A\'ja <PERSON>',
            'team': 'LAS',
            'position': 'F',
            'minutes_per_game': 35.0,
            'games_played': 30,
            'points': 25.0,
            'rebounds': 10.0,
            'assists': 3.0,
            'steals': 1.5,
            'blocks': 2.0,
            'threes': 1.0
        },
        {
            'player_name': '<PERSON><PERSON><PERSON>',
            'team': 'NY',
            'position': 'F',
            'minutes_per_game': 32.0,
            'games_played': 28,
            'points': 20.0,
            'rebounds': 8.0,
            'assists': 4.0,
            'steals': 1.0,
            'blocks': 1.0,
            'threes': 2.5
        },
        {
            'player_name': '<PERSON> <PERSON>escu',
            'team': 'NY',
            'position': 'G',
            'minutes_per_game': 30.0,
            'games_played': 25,
            'points': 18.0,
            'rebounds': 4.0,
            'assists': 8.0,
            'steals': 1.2,
            'blocks': 0.2,
            'threes': 3.0
        }
    ]
    
    # Initialize service
    service = UnifiedNeuralPredictionService()
    
    print("🎯 Testing predictions for different players...")
    print("-" * 50)
    
    all_predictions = []
    
    for player in test_players:
        print(f"\n🏀 {player['player_name']} ({player['position']}, {player['team']})")
        print(f"   Season stats: {player['points']:.1f} pts, {player['rebounds']:.1f} reb, {player['assists']:.1f} ast")
        
        try:
            # Create game data
            game_data = {
                'home_team': 'ATL',
                'away_team': player['team'],
                'league': 'WNBA'
            }

            # Create players data
            players_data = [player]

            # Get predictions
            result = await service.predict_unified(
                game_data=game_data,
                players_data=players_data
            )

            # Extract player props predictions
            predictions = result.get('player_props', {}) if result else {}
            
            if predictions:
                print(f"   🎯 Predictions:")
                for prop_type, pred in predictions.items():
                    if isinstance(pred, dict) and 'prediction' in pred:
                        value = pred['prediction']
                        confidence = pred.get('confidence', 0.0)
                        print(f"      {prop_type}: {value:.1f} (conf: {confidence:.2f})")
                        
                        all_predictions.append({
                            'player': player['player_name'],
                            'prop_type': prop_type,
                            'prediction': value,
                            'confidence': confidence
                        })
                    else:
                        print(f"      {prop_type}: {pred}")
            else:
                print(f"   ❌ No predictions returned")
                
        except Exception as e:
            print(f"   ❌ Error getting predictions: {e}")
    
    # Analyze prediction diversity
    print("\n" + "=" * 50)
    print("📊 PREDICTION ANALYSIS")
    print("=" * 50)
    
    if all_predictions:
        df = pd.DataFrame(all_predictions)
        
        # Check for diversity by prop type
        for prop_type in ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']:
            prop_preds = df[df['prop_type'] == prop_type]['prediction']
            if len(prop_preds) > 0:
                min_val = prop_preds.min()
                max_val = prop_preds.max()
                mean_val = prop_preds.mean()
                std_val = prop_preds.std()
                
                print(f"{prop_type:8}: {min_val:5.1f} - {max_val:5.1f} (mean: {mean_val:5.1f}, std: {std_val:5.2f})")
                
                # Check if all predictions are the same (the old bug)
                if std_val < 0.01:
                    print(f"         ⚠️  WARNING: All {prop_type} predictions are identical!")
                else:
                    print(f"         ✅ Good diversity in {prop_type} predictions")
        
        # Overall assessment
        print("\n🎯 OVERALL ASSESSMENT:")
        unique_predictions = len(df['prediction'].unique())
        total_predictions = len(df)
        
        if unique_predictions == 1:
            print("❌ CRITICAL: All predictions are identical (bug still present)")
        elif unique_predictions < total_predictions * 0.5:
            print("⚠️  WARNING: Low prediction diversity")
        else:
            print("✅ EXCELLENT: Good prediction diversity")
            print("🎉 FEATURE LIST FIX SUCCESSFUL!")
            print("💯 Models are producing realistic, diverse predictions")
    
    else:
        print("❌ No predictions to analyze")

if __name__ == "__main__":
    asyncio.run(test_fixed_inference())
