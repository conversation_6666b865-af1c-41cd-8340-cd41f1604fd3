#!/usr/bin/env python3
"""
Test script to verify the fixed per-game calculation logic
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.neural_cortex.player_props_neural_pipeline import Player<PERSON>ropsNeural<PERSON><PERSON>elin<PERSON>, PlayerPropsConfig

async def test_fixed_per_game_calculation():
    """Test the fixed per-game calculation logic"""
    print("🧪 Testing Fixed Per-Game Calculation Logic")
    print("=" * 60)
    
    # Test with points first (most likely to show the issue)
    prop_type = "points"
    
    try:
        # Create config
        config = PlayerPropsConfig(
            prop_type=prop_type,
            num_epochs=1,  # Just 1 epoch for testing
            batch_size=32,
            learning_rate=0.001,
            hidden_dim=128,
            dropout_rate=0.3,
            loss_function='mse',
            weight_decay=0.01,
            patience=5
        )
        
        # Create pipeline
        pipeline = PlayerPropsNeuralPipeline(config)
        
        print(f"\n🔧 Testing data processing for {prop_type}...")
        
        # Test the data processing method directly
        import pandas as pd
        dummy_data = pd.DataFrame()  # Empty DataFrame since we're loading real data
        
        processed_data = pipeline._process_for_player_props(dummy_data)
        
        print(f"\n📊 RESULTS FOR {prop_type.upper()}:")
        print(f"   Records processed: {len(processed_data)}")
        
        if 'prop_target' in processed_data.columns:
            min_val = processed_data['prop_target'].min()
            max_val = processed_data['prop_target'].max()
            mean_val = processed_data['prop_target'].mean()
            median_val = processed_data['prop_target'].median()
            
            print(f"   Min per-game: {min_val:.2f}")
            print(f"   Max per-game: {max_val:.2f}")
            print(f"   Mean per-game: {mean_val:.2f}")
            print(f"   Median per-game: {median_val:.2f}")
            
            # Check if values are realistic
            if max_val <= 35:  # Realistic max for points
                print(f"   ✅ REALISTIC RANGE - Max {max_val:.1f} is within expected WNBA range")
            else:
                print(f"   ❌ UNREALISTIC RANGE - Max {max_val:.1f} is too high for WNBA per-game")
                
            # Show some sample values
            print(f"\n📈 Sample per-game values:")
            sample_data = processed_data[['player_name', 'prop_target', 'games_played']].head(10)
            for _, row in sample_data.iterrows():
                print(f"   {row['player_name']}: {row['prop_target']:.1f} per game ({row['games_played']} games)")
        
        print(f"\n✅ Per-game calculation test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error testing per-game calculation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_fixed_per_game_calculation())
