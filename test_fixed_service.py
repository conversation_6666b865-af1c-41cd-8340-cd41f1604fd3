#!/usr/bin/env python3
"""
🎯 TEST FIXED UNIFIED SERVICE
Test the fixed service with balanced models against tonight's games
"""

import sys
import asyncio
from pathlib import Path

# Add project root to path
sys.path.append('.')

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

async def test_fixed_service():
    """Test the fixed service"""
    
    print("🎯 TESTING FIXED UNIFIED SERVICE WITH BALANCED MODELS")
    print("=" * 70)
    
    # Sample game data for tonight
    game_data = {
        'home_team': 'LAS',
        'away_team': 'MIN', 
        'game_date': '2025-07-05',
        'home_spread': -2.5,
        'total': 165.5
    }
    
    # Sample players from different tiers
    players_data = [
        {
            'name': 'A\'ja <PERSON>',
            'team': 'LAS',
            'position': 'C',
            'tier': 'Superstar',
            'season_stats': {
                'points': 22.0, 'rebounds': 8.0, 'assists': 3.0,
                'steals': 1.5, 'blocks': 1.5, 'threes': 0.5,
                'games_played': 32
            }
        },
        {
            'name': '<PERSON>',
            'team': 'LAS', 
            'position': 'G',
            'tier': 'Star',
            'season_stats': {
                'points': 18.0, 'rebounds': 4.0, 'assists': 6.0,
                'steals': 2.0, 'blocks': 0.5, 'threes': 3.0,
                'games_played': 30
            }
        },
        {
            'name': 'Napheesa Collier',
            'team': 'MIN',
            'position': 'F',
            'tier': 'Superstar', 
            'season_stats': {
                'points': 24.0, 'rebounds': 8.0, 'assists': 3.0,
                'steals': 2.0, 'blocks': 1.0, 'threes': 2.0,
                'games_played': 32
            }
        },
        {
            'name': 'Kate Martin',
            'team': 'LAS',
            'position': 'G',
            'tier': 'Bench',
            'season_stats': {
                'points': 8.0, 'rebounds': 5.0, 'assists': 2.0,
                'steals': 1.0, 'blocks': 0.2, 'threes': 1.0,
                'games_played': 25
            }
        }
    ]
    
    try:
        # Initialize service
        print("🔄 Initializing UnifiedNeuralPredictionService...")
        service = UnifiedNeuralPredictionService()
        
        # Test prediction
        print("🎯 Getting predictions...")
        result = await service.predict_unified(game_data, players_data)
        
        if result and hasattr(result, 'player_props') and result.player_props:
            print("\n✅ PREDICTIONS SUCCESSFUL!")
            print("=" * 50)

            for player_name, props in result.player_props.items():
                print(f"\n🏀 {player_name}:")

                # Find player tier for context
                player_tier = next((p['tier'] for p in players_data if p['name'] == player_name), 'Unknown')
                print(f"   Tier: {player_tier}")

                for prop_type, prediction in props.items():
                    print(f"   {prop_type.capitalize()}: {prediction:.1f}")

            # Check if predictions are realistic
            print(f"\n🔍 REALISM CHECK:")
            print("-" * 30)

            all_realistic = True
            for player_name, props in result.player_props.items():
                player_tier = next((p['tier'] for p in players_data if p['name'] == player_name), 'Unknown')

                points = props.get('points', 0)
                rebounds = props.get('rebounds', 0)
                assists = props.get('assists', 0)

                # Check if predictions are realistic for tier
                if player_tier == 'Superstar':
                    if points < 15 or points > 30:
                        print(f"   ❌ {player_name} points ({points:.1f}) unrealistic for Superstar")
                        all_realistic = False
                    else:
                        print(f"   ✅ {player_name} points ({points:.1f}) realistic for Superstar")
                elif player_tier == 'Star':
                    if points < 12 or points > 25:
                        print(f"   ❌ {player_name} points ({points:.1f}) unrealistic for Star")
                        all_realistic = False
                    else:
                        print(f"   ✅ {player_name} points ({points:.1f}) realistic for Star")
                elif player_tier == 'Bench':
                    if points < 3 or points > 15:
                        print(f"   ❌ {player_name} points ({points:.1f}) unrealistic for Bench")
                        all_realistic = False
                    else:
                        print(f"   ✅ {player_name} points ({points:.1f}) realistic for Bench")

            if all_realistic:
                print(f"\n🎉 ALL PREDICTIONS ARE REALISTIC!")
                print(f"✅ The balanced models are working correctly!")
            else:
                print(f"\n❌ Some predictions are still unrealistic")
                print(f"🔧 May need further model adjustments")
        
        else:
            print("❌ No predictions returned")
            print("🔧 Check service logs for errors")
    
    except Exception as e:
        print(f"❌ Error testing service: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main execution"""
    asyncio.run(test_fixed_service())

if __name__ == "__main__":
    main()
