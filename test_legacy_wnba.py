#!/usr/bin/env python3
"""
Test legacy WNBA models through the unified neural prediction service.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio

def test_legacy_wnba_models():
    """Test legacy WNBA models through unified service."""
    print("🏀 Testing Legacy WNBA Models...")
    
    try:
        from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
        
        # Initialize service
        service = UnifiedNeuralPredictionService()
        
        # Test data for A'ja <PERSON>
        player_data = {
            'player_id': 1628932,
            'name': "A'ja <PERSON>",  # Use 'name' instead of 'player_name'
            'team_id': 1611661319,
            'team_abbreviation': 'LVA',
            'season_avg_pts': 22.8,
            'season_avg_reb': 11.9,
            'season_avg_ast': 2.3,
            'season_avg_stl': 1.8,
            'season_avg_blk': 2.6,
            'season_avg_fg3m': 0.4,
            'games_played': 38,
            'minutes_per_game': 34.6,
            'usage_rate': 28.5,
            'true_shooting_pct': 0.578,
            'effective_fg_pct': 0.521,
            'league': 'WNBA'
        }
        
        print(f"\n📊 Testing predictions for {player_data['name']}:")
        
        # Test the internal _predict_player_props method directly
        async def test_player_props():
            try:
                predictions, confidences = await service._predict_player_props(player_data)
                
                print(f"✅ Predictions completed!")
                print(f"Predictions: {predictions}")
                print(f"Confidences: {confidences}")
                
                # Validate each prediction
                for prop_type, prediction in predictions.items():
                    confidence = confidences.get(prop_type, 0.0)
                    
                    print(f"  {prop_type.capitalize()}: {prediction:.2f} (confidence: {confidence:.3f})")
                    
                    # Validate prediction is reasonable for WNBA
                    if prop_type == 'points' and not (0 <= prediction <= 40):
                        print(f"    ⚠️  WARNING: {prop_type} prediction {prediction} seems unrealistic for WNBA")
                    elif prop_type == 'rebounds' and not (0 <= prediction <= 20):
                        print(f"    ⚠️  WARNING: {prop_type} prediction {prediction} seems unrealistic for WNBA")
                    elif prop_type == 'assists' and not (0 <= prediction <= 15):
                        print(f"    ⚠️  WARNING: {prop_type} prediction {prediction} seems unrealistic for WNBA")
                    elif prop_type in ['steals', 'blocks', 'threes'] and not (0 <= prediction <= 10):
                        print(f"    ⚠️  WARNING: {prop_type} prediction {prediction} seems unrealistic for WNBA")
                    else:
                        print(f"    ✅ {prop_type} prediction {prediction:.2f} looks reasonable for WNBA")
                        
                return len(predictions) > 0
                
            except Exception as e:
                print(f"❌ Error in player props prediction: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        # Run the test
        success = asyncio.run(test_player_props())
        
        if success:
            print(f"\n✅ Legacy WNBA models working correctly!")
        else:
            print(f"\n❌ Legacy WNBA models failed")
            
    except Exception as e:
        print(f"❌ Error in test setup: {e}")
        import traceback
        traceback.print_exc()

def test_model_paths():
    """Test that the service can find the WNBA models."""
    print("\n🔍 Testing Model Paths...")
    
    try:
        from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
        
        service = UnifiedNeuralPredictionService()
        
        # Check if models are loaded
        print(f"Player props models loaded: {service.player_props_models is not None}")
        
        if service.player_props_models:
            print(f"Available models: {list(service.player_props_models.keys())}")
        
        # Check model paths
        from pathlib import Path
        models_dir = Path("models/real_basketball_models")
        if models_dir.exists():
            model_files = list(models_dir.glob("*.pt"))
            print(f"Found {len(model_files)} model files in {models_dir}")
            for model_file in model_files:
                print(f"  - {model_file.name}")
        else:
            print(f"❌ Models directory not found: {models_dir}")
            
    except Exception as e:
        print(f"❌ Error checking model paths: {e}")

def main():
    """Run all legacy WNBA tests."""
    print("🚀 Starting Legacy WNBA Model Tests...")
    print("=" * 60)
    
    # Test model paths first
    test_model_paths()
    
    # Test legacy models
    test_legacy_wnba_models()
    
    print("\n" + "=" * 60)
    print("✅ Legacy WNBA model tests completed!")

if __name__ == "__main__":
    main()
