#!/usr/bin/env python3
"""
🏀 NBA LIVE API INTEGRATION TEST
===============================

Test script to verify NBA Live API integration with the Queen's prediction system.
"""

import asyncio
import sys
import os

# Add project root to path
sys.path.append('.')

from get_queens_predictions_tonight import QueensPredictionOracle

async def test_nba_integration():
    """Test NBA Live API integration"""
    print("🏀 TESTING NBA LIVE API INTEGRATION")
    print("=" * 50)
    print()
    
    # Initialize the Queen's Oracle
    oracle = QueensPredictionOracle()
    
    print("1. Initializing Queen's systems...")
    if not await oracle.initialize():
        print("❌ Failed to initialize Queen's systems")
        return
    print("✅ Queen's systems initialized successfully")
    print()
    
    # Test NBA Live API connection
    print("2. Testing NBA Live API connection...")
    try:
        nba_games = await oracle.get_tonights_nba_games()
        print(f"✅ NBA Live API connection successful")
        print(f"📊 Found {len(nba_games)} NBA games")
        
        if nba_games:
            print("\n🏀 NBA Games Found:")
            for i, game in enumerate(nba_games, 1):
                print(f"   Game {i}: {game['away_team']} @ {game['home_team']}")
                print(f"   Status: {game.get('status', 'Unknown')}")
                print(f"   Time: {game.get('time', 'TBD')}")
                if game.get('home_score', 0) > 0 or game.get('away_score', 0) > 0:
                    print(f"   Score: {game['away_team']} {game.get('away_score', 0)} - {game.get('home_score', 0)} {game['home_team']}")
                print()
        else:
            print("📅 No NBA games found for tonight")
            
    except Exception as e:
        print(f"❌ NBA Live API connection failed: {e}")
    
    print()
    
    # Test WNBA connection for comparison
    print("3. Testing WNBA connection for comparison...")
    try:
        wnba_games = await oracle.get_tonights_wnba_games()
        print(f"✅ WNBA connection successful")
        print(f"📊 Found {len(wnba_games)} WNBA games")
        
        if wnba_games:
            print("\n🏀 WNBA Games Found:")
            for i, game in enumerate(wnba_games, 1):
                print(f"   Game {i}: {game['away_team']} @ {game['home_team']}")
                print(f"   Time: {game.get('time', 'TBD')}")
                print()
        else:
            print("📅 No WNBA games found for tonight")
            
    except Exception as e:
        print(f"❌ WNBA connection failed: {e}")
    
    print()
    
    # Test combined league functionality
    print("4. Testing combined league functionality...")
    try:
        all_games = await oracle.get_tonights_games("BOTH")
        print(f"✅ Combined league functionality working")
        print(f"📊 Total games found: {len(all_games)}")
        
        nba_count = len([g for g in all_games if g.get('league') == 'NBA'])
        wnba_count = len([g for g in all_games if g.get('league') == 'WNBA'])
        
        print(f"   NBA games: {nba_count}")
        print(f"   WNBA games: {wnba_count}")
        
    except Exception as e:
        print(f"❌ Combined league functionality failed: {e}")
    
    print()
    print("🎯 NBA Live API Integration Test Complete!")

if __name__ == "__main__":
    asyncio.run(test_nba_integration())
