#!/usr/bin/env python3
"""
🔧 DIRECT PLAYER PROPS TEST
Test the player props models directly to see if they're working
"""

import sys
import torch
import numpy as np
from pathlib import Path

# Add project root to path
sys.path.append('.')

print("🔧 DIRECT PLAYER PROPS MODEL TEST")
print("=" * 50)

def test_direct_model_inference():
    """Test models directly with dummy data"""
    
    prop_types = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
    
    # Create dummy input (30 features as expected by models)
    dummy_features = torch.randn(1, 30)
    
    print(f"🎯 Testing {len(prop_types)} player props models")
    print(f"📊 Input shape: {dummy_features.shape}")
    
    results = {}
    
    for prop_type in prop_types:
        model_path = f"models/player_props/nba_{prop_type}/best_{prop_type}_model.pt"
        
        print(f"\n🔍 Testing {prop_type} model...")
        
        if not Path(model_path).exists():
            print(f"   ❌ Model file not found: {model_path}")
            results[prop_type] = None
            continue
        
        try:
            # Load checkpoint
            checkpoint = torch.load(model_path, map_location='cpu')
            
            # Check if we have the required components
            if 'model_state_dict' not in checkpoint:
                print(f"   ❌ No model_state_dict in checkpoint")
                results[prop_type] = None
                continue
            
            # Recreate model architecture based on config
            from torch import nn
            
            # Get config if available
            config = checkpoint.get('config', {})
            hidden_dim = config.get('hidden_dim', 128)
            num_layers = config.get('num_layers', 3)
            dropout_rate = config.get('dropout_rate', 0.2)
            
            # Create model architecture
            layers = []
            input_dim = 30
            
            for i in range(num_layers):
                if i == 0:
                    layers.extend([
                        nn.Linear(input_dim, hidden_dim),
                        nn.ReLU(),
                        nn.Dropout(dropout_rate)
                    ])
                else:
                    layers.extend([
                        nn.Linear(hidden_dim, hidden_dim // 2),
                        nn.ReLU(),
                        nn.Dropout(dropout_rate)
                    ])
                    hidden_dim = hidden_dim // 2
            
            # Output layer (linear for regression)
            layers.append(nn.Linear(hidden_dim, 1))
            
            model = nn.Sequential(*layers)
            
            # Load state dict
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval()
            
            # Make prediction
            with torch.no_grad():
                raw_output = model(dummy_features)
                raw_value = raw_output.item()
            
            # Apply target unscaling if available
            if 'target_scaler_params' in checkpoint:
                scaler_params = checkpoint['target_scaler_params']
                mean = scaler_params.get('mean', 0)
                scale = scaler_params.get('scale', 1)
                unscaled_value = (raw_value * scale) + mean
            else:
                unscaled_value = raw_value
            
            results[prop_type] = unscaled_value
            
            print(f"   ✅ Model loaded and inference successful")
            print(f"   📊 Raw output: {raw_value:.3f}")
            print(f"   📊 Unscaled output: {unscaled_value:.3f}")
            
            # Check target scaler info
            if 'target_scaler_params' in checkpoint:
                scaler = checkpoint['target_scaler_params']
                print(f"   📊 Target scaler: mean={scaler.get('mean', 'N/A'):.3f}, scale={scaler.get('scale', 'N/A'):.3f}")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results[prop_type] = None
    
    return results

def test_with_realistic_features():
    """Test with more realistic player features"""
    
    print(f"\n🎯 TESTING WITH REALISTIC PLAYER FEATURES")
    print("-" * 50)
    
    # Create realistic features for different player types
    test_players = [
        {
            'name': 'Superstar Player',
            'features': np.array([
                # Basic stats (season averages converted to per-game)
                20.0,  # points
                8.0,   # rebounds  
                5.0,   # assists
                1.5,   # steals
                1.0,   # blocks
                2.0,   # threes
                # Additional features (mock realistic values)
                35.0,  # minutes
                0.45,  # fg_pct
                0.35,  # three_pct
                0.80,  # ft_pct
                # More features to reach 30 total
                *[0.5] * 20  # Fill remaining with neutral values
            ])
        },
        {
            'name': 'Role Player',
            'features': np.array([
                # Basic stats
                8.0,   # points
                4.0,   # rebounds
                2.0,   # assists
                0.8,   # steals
                0.3,   # blocks
                1.0,   # threes
                # Additional features
                20.0,  # minutes
                0.42,  # fg_pct
                0.32,  # three_pct
                0.75,  # ft_pct
                # Fill remaining
                *[0.3] * 20
            ])
        }
    ]
    
    prop_types = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
    
    for player in test_players:
        print(f"\n🏀 Testing {player['name']}:")
        
        # Convert to tensor
        features_tensor = torch.tensor(player['features'], dtype=torch.float32).unsqueeze(0)
        
        player_predictions = {}
        
        for prop_type in prop_types:
            model_path = f"models/player_props/nba_{prop_type}/best_{prop_type}_model.pt"
            
            if not Path(model_path).exists():
                player_predictions[prop_type] = None
                continue
            
            try:
                # Load and predict (simplified version)
                checkpoint = torch.load(model_path, map_location='cpu')
                
                # Quick model recreation (simplified)
                from torch import nn
                model = nn.Sequential(
                    nn.Linear(30, 128),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(128, 64),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(64, 32),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(32, 1)
                )
                
                model.load_state_dict(checkpoint['model_state_dict'])
                model.eval()
                
                with torch.no_grad():
                    raw_output = model(features_tensor)
                    raw_value = raw_output.item()
                
                # Unscale
                if 'target_scaler_params' in checkpoint:
                    scaler_params = checkpoint['target_scaler_params']
                    mean = scaler_params.get('mean', 0)
                    scale = scaler_params.get('scale', 1)
                    unscaled_value = (raw_value * scale) + mean
                else:
                    unscaled_value = raw_value
                
                player_predictions[prop_type] = unscaled_value
                
            except Exception as e:
                print(f"   ❌ {prop_type} error: {e}")
                player_predictions[prop_type] = None
        
        # Display results
        print(f"   📊 Predictions:")
        for prop_type in prop_types:
            value = player_predictions.get(prop_type)
            if value is not None:
                print(f"      {prop_type.capitalize()}: {value:.1f}")
            else:
                print(f"      {prop_type.capitalize()}: ERROR")

def main():
    """Main execution"""
    
    print(f"🎯 Testing player props models directly")
    
    # Test basic model loading and inference
    results = test_direct_model_inference()
    
    # Count successful models
    successful = sum(1 for v in results.values() if v is not None)
    total = len(results)
    
    print(f"\n📊 SUMMARY:")
    print(f"✅ Successful models: {successful}/{total}")
    
    if successful > 0:
        print(f"✅ Models are functional!")
        
        # Test with realistic features
        test_with_realistic_features()
        
        print(f"\n🎯 DIAGNOSIS:")
        print(f"1. ✅ Models can be loaded and make predictions")
        print(f"2. ⚠️ Issue may be in UnifiedNeuralPredictionService integration")
        print(f"3. 🔍 Check feature preparation in the service")
        print(f"4. 🔍 Check player props model loading in the service")
    else:
        print(f"❌ Models are not functional")
        print(f"🔍 Check model training and saving process")

if __name__ == "__main__":
    main()
