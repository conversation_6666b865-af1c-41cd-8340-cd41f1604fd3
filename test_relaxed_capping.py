#!/usr/bin/env python3
"""
Quick test to validate relaxed capping is working
"""

import sys
sys.path.append('.')

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_relaxed_capping():
    """Test that very relaxed capping allows maximum prediction diversity"""

    print("🧪 TESTING VERY RELAXED CAPPING LOGIC")
    print("=" * 60)

    # Initialize service
    service = UnifiedNeuralPredictionService()

    # Test players with different characteristics
    test_players = [
        "<PERSON>",  # Should show 62.1 points now
        "Bridget Carleton",  # Should show 56.3 points now
        "Diamond Miller",  # Should show 58.1 points now
    ]

    print("\n📊 TESTING VERY RELAXED CAPPING:")
    print("-" * 40)

    for player_name in test_players:
        print(f"\n👤 {player_name}:")

        # Test points prediction - should now show natural model outputs
        try:
            props, _ = await service._predict_player_props({'name': player_name})
            points = props.get('points', 0)
            rebounds = props.get('rebounds', 0)
            assists = props.get('assists', 0)
            print(f"  Points: {points:.1f}")
            print(f"  Rebounds: {rebounds:.1f}")
            print(f"  Assists: {assists:.1f}")

        except Exception as e:
            print(f"  Error: {e}")

    print("\n✅ Very relaxed capping test complete!")

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_relaxed_capping())
