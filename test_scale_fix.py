#!/usr/bin/env python3
"""
Test script to verify the scale fix for player props predictions.
This tests that the models now output realistic per-game predictions instead of identical values.
"""

import sys
import logging
from pathlib import Path

# Add project root to path
sys.path.append('.')

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_scale_fix():
    """Test that the scale fix produces realistic and different predictions for different players"""
    
    print("🔍 TESTING SCALE FIX FOR PLAYER PROPS PREDICTIONS")
    print("=" * 60)
    
    # Initialize the service
    service = UnifiedNeuralPredictionService()
    
    # Test players with different characteristics
    test_players = [
        {"name": "A'ja <PERSON>", "tier": 1, "position": "F"},  # Superstar
        {"name": "<PERSON><PERSON><PERSON>", "tier": 1, "position": "F"},  # Superstar
        {"name": "<PERSON>", "tier": 2, "position": "G"},  # Star
        {"name": "<PERSON>", "tier": 3, "position": "G"},  # Average
        {"name": "Rookie Player", "tier": 4, "position": "G"},  # Bench
    ]
    
    print(f"\n🏀 Testing {len(test_players)} players across different tiers...")
    
    all_predictions = {}
    
    for player in test_players:
        print(f"\n👤 Testing {player['name']} (Tier {player['tier']}, {player['position']})")
        print("-" * 50)
        
        try:
            # Get predictions for all prop types
            predictions, confidences = service.predict_player_props(player)
            
            all_predictions[player['name']] = predictions
            
            print(f"📊 Predictions for {player['name']}:")
            for prop_type, prediction in predictions.items():
                confidence = confidences.get(prop_type, 0.0)
                print(f"  {prop_type:8}: {prediction:5.1f} (confidence: {confidence:.1%})")
                
        except Exception as e:
            print(f"❌ Error predicting for {player['name']}: {e}")
            continue
    
    # Analyze results
    print("\n" + "=" * 60)
    print("🔍 ANALYSIS: Checking for diversity in predictions")
    print("=" * 60)
    
    prop_types = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
    
    for prop_type in prop_types:
        values = []
        for player_name, predictions in all_predictions.items():
            if prop_type in predictions:
                values.append(predictions[prop_type])
        
        if values:
            min_val = min(values)
            max_val = max(values)
            avg_val = sum(values) / len(values)
            unique_values = len(set(values))
            
            print(f"\n{prop_type.upper()}:")
            print(f"  Range: {min_val:.1f} - {max_val:.1f}")
            print(f"  Average: {avg_val:.1f}")
            print(f"  Unique values: {unique_values}/{len(values)}")
            
            # Check if predictions are realistic
            realistic_ranges = {
                'points': (5, 35),
                'rebounds': (2, 15),
                'assists': (1, 12),
                'steals': (0.5, 4),
                'blocks': (0.2, 3),
                'threes': (0.5, 8)
            }
            
            expected_min, expected_max = realistic_ranges.get(prop_type, (0, 50))
            
            if min_val >= expected_min and max_val <= expected_max:
                print(f"  ✅ Values are in realistic range ({expected_min}-{expected_max})")
            else:
                print(f"  ⚠️ Some values outside realistic range ({expected_min}-{expected_max})")
            
            if unique_values > 1:
                print(f"  ✅ Predictions are diverse (not identical)")
            else:
                print(f"  ❌ All predictions are identical!")
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 SUMMARY")
    print("=" * 60)
    
    total_predictions = sum(len(predictions) for predictions in all_predictions.values())
    successful_players = len([p for p in all_predictions.values() if p])
    
    print(f"✅ Successfully generated {total_predictions} predictions")
    print(f"✅ {successful_players}/{len(test_players)} players had successful predictions")
    
    # Check if the scale fix worked
    all_values = []
    for predictions in all_predictions.values():
        all_values.extend(predictions.values())
    
    if all_values:
        if len(set(all_values)) > 1:
            print("✅ SCALE FIX SUCCESS: Predictions are diverse!")
        else:
            print("❌ SCALE FIX FAILED: All predictions are still identical!")
        
        if all(0 <= v <= 50 for v in all_values):
            print("✅ SCALE FIX SUCCESS: All predictions are in realistic per-game ranges!")
        else:
            print("⚠️ Some predictions may still be on wrong scale")
    
    return all_predictions

if __name__ == "__main__":
    test_scale_fix()
