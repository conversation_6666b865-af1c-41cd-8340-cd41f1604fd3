#!/usr/bin/env python3
"""
Direct test of the scale fix by calling the internal prediction methods
"""

import sys
import os
import logging
import numpy as np
import torch
import asyncio

# Add project root to path
sys.path.insert(0, os.path.abspath('.'))

# Set up basic logging
logging.basicConfig(level=logging.INFO)

async def test_scale_fix_direct():
    try:
        print("🔍 Testing scale fix directly...")
        
        # Import the service
        from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
        print("✅ Import successful")
        
        # Initialize service
        service = UnifiedNeuralPredictionService()
        print("✅ Service initialized")

        # Check if models are loaded
        print(f"🔍 Player props models loaded: {len(service.player_props_models)}")
        print(f"🔍 Available prop types: {list(service.player_props_models.keys())}")

        if not service.player_props_models:
            print("❌ No player props models loaded! Attempting to load...")
            await service._load_player_props_models()
            print(f"🔍 After loading: {len(service.player_props_models)} models")
        
        # Test different players to see if we get different predictions
        test_players = [
            {"name": "<PERSON><PERSON>j<PERSON> <PERSON>", "team": "Las Vegas Aces", "position": "<PERSON>"},
            {"name": "<PERSON> <PERSON><PERSON>", "team": "Las Vegas Aces", "position": "G"},
            {"name": "Breanna <PERSON>", "team": "New York Liberty", "position": "F"},
            {"name": "<PERSON> <PERSON><PERSON>", "team": "New York Liberty", "position": "G"}
        ]
        
        prop_types = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
        all_predictions = {}
        
        print(f"\n🏀 Testing {len(test_players)} players for scale fix...")
        
        for player in test_players:
            print(f"\n👤 Testing {player['name']}...")
            player_predictions = {}
            
            try:
                # Call the async prediction method
                predictions, confidences = await service._predict_player_props(player)
                player_predictions = predictions

                for prop_type, prediction in predictions.items():
                    print(f"  {prop_type:8}: {prediction:5.1f}")

            except Exception as e:
                print(f"  ERROR: {e}")
                continue
            
            all_predictions[player['name']] = player_predictions
        
        # Analyze results
        print("\n" + "=" * 60)
        print("🔍 SCALE FIX ANALYSIS")
        print("=" * 60)
        
        for prop_type in prop_types:
            values = []
            for player_name, predictions in all_predictions.items():
                if prop_type in predictions:
                    values.append(predictions[prop_type])
            
            if values:
                min_val = min(values)
                max_val = max(values)
                avg_val = sum(values) / len(values)
                unique_values = len(set(values))
                
                print(f"\n{prop_type.upper()}:")
                print(f"  Values: {[f'{v:.1f}' for v in values]}")
                print(f"  Range: {min_val:.1f} - {max_val:.1f}")
                print(f"  Average: {avg_val:.1f}")
                print(f"  Unique: {unique_values}/{len(values)}")
                
                # Check if realistic
                realistic_ranges = {
                    'points': (5, 35),
                    'rebounds': (2, 15),
                    'assists': (1, 12),
                    'steals': (0.5, 4),
                    'blocks': (0.2, 3),
                    'threes': (0.5, 8)
                }
                
                expected_min, expected_max = realistic_ranges.get(prop_type, (0, 50))
                
                if min_val >= expected_min and max_val <= expected_max:
                    print(f"  ✅ Realistic range ({expected_min}-{expected_max})")
                else:
                    print(f"  ⚠️ Outside realistic range ({expected_min}-{expected_max})")
                
                if unique_values > 1:
                    print(f"  ✅ Diverse predictions")
                else:
                    print(f"  ❌ All identical!")
        
        # Overall summary
        print("\n" + "=" * 60)
        print("📋 OVERALL SUMMARY")
        print("=" * 60)
        
        total_predictions = sum(len(predictions) for predictions in all_predictions.values())
        all_values = []
        for predictions in all_predictions.values():
            all_values.extend(predictions.values())
        
        if all_values:
            unique_count = len(set(all_values))
            print(f"✅ Generated {total_predictions} predictions")
            print(f"✅ {unique_count} unique values out of {len(all_values)} total")
            print(f"✅ Range: {min(all_values):.1f} - {max(all_values):.1f}")
            
            if unique_count > 1:
                print("🎉 SCALE FIX SUCCESS: Predictions are diverse!")
            else:
                print("❌ SCALE FIX FAILED: All predictions identical!")
            
            if all(0 <= v <= 50 for v in all_values):
                print("🎉 SCALE FIX SUCCESS: All values in realistic per-game ranges!")
            else:
                unrealistic = [v for v in all_values if not (0 <= v <= 50)]
                print(f"⚠️ {len(unrealistic)} unrealistic values: {unrealistic}")
        else:
            print("❌ No predictions generated!")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_scale_fix_direct())
