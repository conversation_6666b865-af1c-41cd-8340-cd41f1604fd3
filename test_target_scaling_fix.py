#!/usr/bin/env python3
"""
🎯 Test Target Scaling Fix
Test that the unified neural prediction service now properly unscales predictions
"""

import sys
import asyncio
import logging
import numpy as np
from pathlib import Path

# Add project root to path
sys.path.append('.')

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_target_scaling_fix():
    """Test that target scaling is working correctly"""
    print("🎯 TESTING TARGET SCALING FIX")
    print("=" * 60)
    
    try:
        # Initialize service
        service = UnifiedNeuralPredictionService(league="WNBA")
        
        # Initialize models
        success = await service.initialize()
        if not success:
            print("❌ Failed to initialize service")
            return
        
        print("✅ Service initialized successfully")
        
        # Test with <PERSON> (known player from validation)
        test_player = {
            'name': '<PERSON>',
            'team': 'Golden State Valkyries',
            'position': 'G',
            'minutes_per_game': 25.0,
            'points': 8.5,
            'rebounds': 3.2,
            'assists': 2.1,
            'steals': 1.0,
            'blocks': 0.2,
            'threes': 1.5,
            'field_goal_percentage': 0.45,
            'free_throw_percentage': 0.80,
            'age': 24
        }
        
        print(f"\n🏀 Testing predictions for {test_player['name']}")
        print("-" * 40)
        
        # Get player props predictions
        props_predictions, props_confidence = await service._predict_player_props(test_player)
        
        if props_predictions:
            print("✅ PREDICTIONS GENERATED:")
            for prop_type, prediction in props_predictions.items():
                confidence = props_confidence.get(prop_type, 0.0)
                print(f"   {prop_type}: {prediction:.1f} (confidence: {confidence:.3f})")
                
                # Check if predictions are realistic (not clipped maximums)
                realistic_ranges = {
                    'points': (0, 35),
                    'rebounds': (0, 15),
                    'assists': (0, 10),
                    'steals': (0, 5),
                    'blocks': (0, 3),
                    'threes': (0, 6)
                }
                
                if prop_type in realistic_ranges:
                    min_val, max_val = realistic_ranges[prop_type]
                    if min_val <= prediction <= max_val:
                        print(f"      ✅ {prop_type} prediction {prediction:.1f} is in realistic range [{min_val}-{max_val}]")
                    else:
                        print(f"      ❌ {prop_type} prediction {prediction:.1f} is outside realistic range [{min_val}-{max_val}]")
                        
                # Check if prediction is exactly at clipping maximum (indicates scaling issue)
                clipping_maximums = {
                    'points': 70.0,
                    'rebounds': 35.0,
                    'assists': 25.0,
                    'steals': 10.0,
                    'blocks': 10.0,
                    'threes': 15.0
                }
                
                if prop_type in clipping_maximums and abs(prediction - clipping_maximums[prop_type]) < 0.1:
                    print(f"      🚨 WARNING: {prop_type} prediction {prediction:.1f} is at clipping maximum {clipping_maximums[prop_type]} - scaling issue!")
        else:
            print("❌ No predictions generated")
            
        print("\n🎯 TARGET SCALING FIX TEST COMPLETE")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_target_scaling_fix())
