#!/usr/bin/env python3
"""
Test script to verify team total predictions are realistic after fixing the training data.
"""

import sys
from pathlib import Path
import logging

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from real_basketball_unified_prediction_service import RealBasketballUnifiedService

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

def test_team_total_predictions():
    """Test team total predictions for realistic WNBA scores."""
    logger.info("🏀 Testing Team Total Predictions...")

    try:
        # Initialize the prediction service
        logger.info("🔧 Initializing prediction service...")
        service = RealBasketballUnifiedService()
        logger.info("✅ Service initialized successfully")
    except Exception as e:
        logger.error(f"❌ Failed to initialize service: {e}")
        return
    
    # Test with some WNBA teams
    test_matchups = [
        ("LVA", "NY"),    # Las Vegas Aces vs New York Liberty
        ("MIN", "CONN"),  # Minnesota Lynx vs Connecticut Sun
        ("ATL", "IND"),   # Atlanta Dream vs Indiana Fever
        ("CHI", "LAS"),   # Chicago Sky vs Los Angeles Sparks
        ("DAL", "WAS")    # Dallas Wings vs Washington Mystics
    ]
    
    logger.info("🎯 Testing team total predictions for WNBA matchups:")
    logger.info("=" * 60)
    
    for home_team, away_team in test_matchups:
        try:
            # Get team total predictions
            logger.info(f"🔍 Predicting totals for {home_team} vs {away_team}...")
            home_total, away_total = service._predict_team_totals(home_team, away_team)
            logger.info(f"🔍 Raw predictions: {home_total}, {away_total}")
            
            # Calculate game total
            game_total = home_total + away_total
            
            logger.info(f"🏀 {home_team} vs {away_team}:")
            logger.info(f"   Home ({home_team}): {home_total:.1f} points")
            logger.info(f"   Away ({away_team}): {away_total:.1f} points")
            logger.info(f"   Game Total: {game_total:.1f} points")
            
            # Check if predictions are realistic for WNBA
            if 65 <= home_total <= 100 and 65 <= away_total <= 100:
                logger.info(f"   ✅ Realistic WNBA range")
            else:
                logger.warning(f"   ⚠️  Outside typical WNBA range (65-100)")
            
            logger.info("-" * 40)
            
        except Exception as e:
            logger.error(f"❌ Error predicting {home_team} vs {away_team}: {e}")
    
    logger.info("🏀 Team total prediction test complete!")

if __name__ == "__main__":
    test_team_total_predictions()
