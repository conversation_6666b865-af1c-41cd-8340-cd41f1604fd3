#!/usr/bin/env python3
"""
Test if there are games tomorrow and test our scale fix
"""

import sys
import os
import asyncio
import logging
from datetime import datetime, date, timedelta

# Add project root to path
sys.path.insert(0, os.path.abspath('.'))

# Set up basic logging
logging.basicConfig(level=logging.INFO)

async def test_tomorrow_games():
    try:
        print("🔍 Testing games for tomorrow...")
        
        # Import the service
        from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
        from src.data.basketball_data_loader import BasketballDataLoader
        print("✅ Imports successful")
        
        # Initialize data loader
        data_loader = BasketballDataLoader()
        print("✅ Data loader initialized")
        
        # Check tomorrow's date
        tomorrow = date.today() + timedelta(days=1)
        tomorrow_str = tomorrow.strftime("%Y-%m-%d")
        print(f"🗓️ Checking games for: {tomorrow_str}")
        
        # Get games for tomorrow
        games = await data_loader.get_games_for_date(tomorrow_str, league="WNBA")
        print(f"🏀 Found {len(games)} games for {tomorrow_str}")
        
        if games:
            for i, game in enumerate(games):
                print(f"  Game {i+1}: {game.get('away_team', 'Unknown')} @ {game.get('home_team', 'Unknown')}")
                print(f"    Time: {game.get('commence_time', 'TBD')}")
                print(f"    Odds: {game.get('odds', 'No odds')}")
        
        # If we have games, test our scale fix
        if games:
            print(f"\n🧠 Testing scale fix with tomorrow's games...")
            
            # Initialize unified service
            service = UnifiedNeuralPredictionService()
            await service._load_player_props_models()
            print(f"✅ Loaded {len(service.player_props_models)} player props models")
            
            # Test with first game
            test_game = games[0]
            test_players = [
                {"name": "A'ja Wilson", "team": test_game.get('home_team'), "position": "F"},
                {"name": "Kelsey Plum", "team": test_game.get('home_team'), "position": "G"},
                {"name": "Breanna Stewart", "team": test_game.get('away_team'), "position": "F"},
                {"name": "Sabrina Ionescu", "team": test_game.get('away_team'), "position": "G"}
            ]
            
            print(f"\n🏀 Testing unified prediction for {test_game.get('away_team')} @ {test_game.get('home_team')}...")
            
            # Get unified predictions
            result = await service.predict_unified(test_game, test_players)
            
            print("📊 Player Props Results:")
            if hasattr(result, 'player_props') and result.player_props:
                all_predictions = []
                
                for player_name, props in result.player_props.items():
                    print(f"\n👤 {player_name}:")
                    for prop_type, prediction in props.items():
                        print(f"  {prop_type:8}: {prediction:5.1f}")
                        all_predictions.append(prediction)
                
                # Analyze diversity
                unique_values = len(set(all_predictions))
                print(f"\n🔍 Scale Fix Analysis:")
                print(f"  Total predictions: {len(all_predictions)}")
                print(f"  Unique values: {unique_values}")
                print(f"  Range: {min(all_predictions):.1f} - {max(all_predictions):.1f}")
                
                if unique_values > 1:
                    print("🎉 SCALE FIX SUCCESS: Predictions are diverse!")
                else:
                    print("❌ SCALE FIX FAILED: All predictions identical!")
                
                if all(0 <= v <= 50 for v in all_predictions):
                    print("🎉 SCALE FIX SUCCESS: All values in realistic per-game ranges!")
                else:
                    print("⚠️ Some predictions may be unrealistic")
            else:
                print("❌ No player props found in result")
        else:
            print("❌ No games found for tomorrow - cannot test scale fix with real games")
            
            # Test with mock game data instead
            print("\n🧠 Testing scale fix with mock game data...")
            
            service = UnifiedNeuralPredictionService()
            await service._load_player_props_models()
            
            mock_game = {
                "home_team": "Las Vegas Aces",
                "away_team": "Connecticut Sun",
                "game_date": tomorrow_str,
                "league": "WNBA"
            }
            
            test_players = [
                {"name": "A'ja Wilson", "team": "Las Vegas Aces", "position": "F"},
                {"name": "Kelsey Plum", "team": "Las Vegas Aces", "position": "G"},
                {"name": "DeWanna Bonner", "team": "Connecticut Sun", "position": "F"},
                {"name": "Alyssa Thomas", "team": "Connecticut Sun", "position": "F"}
            ]
            
            result = await service.predict_unified(mock_game, test_players)
            
            print("📊 Mock Game Player Props Results:")
            if hasattr(result, 'player_props') and result.player_props:
                all_predictions = []
                
                for player_name, props in result.player_props.items():
                    print(f"\n👤 {player_name}:")
                    for prop_type, prediction in props.items():
                        print(f"  {prop_type:8}: {prediction:5.1f}")
                        all_predictions.append(prediction)
                
                # Analyze diversity
                unique_values = len(set(all_predictions))
                print(f"\n🔍 Scale Fix Analysis:")
                print(f"  Total predictions: {len(all_predictions)}")
                print(f"  Unique values: {unique_values}")
                print(f"  Range: {min(all_predictions):.1f} - {max(all_predictions):.1f}")
                
                if unique_values > 1:
                    print("🎉 SCALE FIX SUCCESS: Predictions are diverse!")
                else:
                    print("❌ SCALE FIX FAILED: All predictions identical!")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_tomorrow_games())
