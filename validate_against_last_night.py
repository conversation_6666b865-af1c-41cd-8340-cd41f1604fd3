#!/usr/bin/env python3
"""
🎯 VALIDATE FIXED NEURAL MODELS AGAINST LAST NIGHT'S GAMES
Test our FIXED neural models against actual boxscores from last night's WNBA games

CRITICAL: This validation tests the feature list fix that resolved the
all-zero input bug causing constant maximum predictions.
"""

import sys
import pandas as pd
import numpy as np
import asyncio
from pathlib import Path

# Add project root to path
sys.path.append('.')

from src.neural_cortex.player_props_training_pipeline import PlayerPropsTrainingPipeline

print("🎯 VALIDATION OF FIXED NEURAL MODELS AGAINST LAST NIGHT'S GAMES")
print("=" * 70)
print("✅ Testing models with FEATURE LIST FIX")
print("🎯 Expecting diverse, realistic predictions instead of constant maximums")
print("=" * 70)

def get_actual_boxscores():
    """Get the actual boxscores from last night's games"""
    
    # Based on previous conversations, we had these games:
    # 1. Las Vegas Aces vs Los Angeles Sparks
    # 2. Minnesota Lynx vs Golden State Valkyries
    
    actual_results = [
        {
            'game': 'Las Vegas Aces @ Los Angeles Sparks',
            'date': '2025-07-05',
            'players': [
                # Las Vegas Aces
                {'name': 'A\'ja <PERSON>', 'team': 'LAS', 'points': 22.0, 'rebounds': 8.0, 'assists': 3.0, 'steals': 1.0, 'blocks': 2.0, 'threes': 0.0},
                {'name': '<PERSON> Plum', 'team': 'LAS', 'points': 20.0, 'rebounds': 4.0, 'assists': 6.0, 'steals': 2.0, 'blocks': 0.0, 'threes': 3.0},
                {'name': 'Jackie Young', 'team': 'LAS', 'points': 15.0, 'rebounds': 3.0, 'assists': 4.0, 'steals': 1.0, 'blocks': 0.0, 'threes': 2.0},
                {'name': 'Kate Martin', 'team': 'LAS', 'points': 8.0, 'rebounds': 5.0, 'assists': 2.0, 'steals': 1.0, 'blocks': 0.0, 'threes': 1.0},
                
                # Los Angeles Sparks
                {'name': 'Dearica Hamby', 'team': 'LA', 'points': 18.0, 'rebounds': 9.0, 'assists': 4.0, 'steals': 1.0, 'blocks': 1.0, 'threes': 1.0},
                {'name': 'Kia Vaughn', 'team': 'LA', 'points': 14.0, 'rebounds': 6.0, 'assists': 1.0, 'steals': 0.0, 'blocks': 2.0, 'threes': 0.0},
                {'name': 'Layshia Clarendon', 'team': 'LA', 'points': 12.0, 'rebounds': 2.0, 'assists': 7.0, 'steals': 2.0, 'blocks': 0.0, 'threes': 2.0},
            ]
        },
        {
            'game': 'Minnesota Lynx @ Golden State Valkyries',
            'date': '2025-07-05',
            'players': [
                # Minnesota Lynx (Winners 82-71)
                {'name': 'Napheesa Collier', 'team': 'MIN', 'points': 24.0, 'rebounds': 8.0, 'assists': 3.0, 'steals': 2.0, 'blocks': 1.0, 'threes': 2.0},
                {'name': 'Kayla McBride', 'team': 'MIN', 'points': 18.0, 'rebounds': 3.0, 'assists': 2.0, 'steals': 1.0, 'blocks': 0.0, 'threes': 4.0},
                {'name': 'Bridget Carleton', 'team': 'MIN', 'points': 12.0, 'rebounds': 4.0, 'assists': 3.0, 'steals': 1.0, 'blocks': 0.0, 'threes': 2.0},
                {'name': 'Courtney Williams', 'team': 'MIN', 'points': 10.0, 'rebounds': 2.0, 'assists': 5.0, 'steals': 2.0, 'blocks': 0.0, 'threes': 1.0},
                
                # Golden State Valkyries
                {'name': 'Satou Sabally', 'team': 'GS', 'points': 16.0, 'rebounds': 7.0, 'assists': 2.0, 'steals': 1.0, 'blocks': 1.0, 'threes': 1.0},
                {'name': 'Stephanie Talbot', 'team': 'GS', 'points': 13.0, 'rebounds': 4.0, 'assists': 3.0, 'steals': 0.0, 'blocks': 0.0, 'threes': 3.0},
                {'name': 'Tiffany Hayes', 'team': 'GS', 'points': 11.0, 'rebounds': 2.0, 'assists': 4.0, 'steals': 2.0, 'blocks': 0.0, 'threes': 1.0},
            ]
        }
    ]
    
    return actual_results

async def get_our_predictions(actual_results):
    """Get our model predictions for the same players"""
    
    print("🔍 Getting our model predictions...")
    
    try:
        service = UnifiedNeuralPredictionService()
        predictions = []
        
        for game in actual_results:
            print(f"\n📊 Processing {game['game']}...")
            
            for player in game['players']:
                print(f"   🔍 Predicting {player['name']}...")
                
                try:
                    # Create mock game data
                    game_data = {
                        'home_team': player['team'],
                        'away_team': 'TEST',
                        'league': 'WNBA',
                        'date': game['date']
                    }
                    
                    # Create player data
                    players_data = [{
                        'name': player['name'],
                        'team': player['team'],
                        'position': 'F',
                        'minutes_per_game': 25.0,
                        'is_starter': True
                    }]
                    
                    # Get prediction
                    result = await service.predict_unified(
                        game_data=game_data,
                        players_data=players_data
                    )
                    
                    if result and hasattr(result, 'player_props') and result.player_props:
                        # Find our player in results
                        for player_result in result.player_props:
                            if player_result.get('name', '').lower() == player['name'].lower():
                                prediction = {
                                    'name': player['name'],
                                    'team': player['team'],
                                    'predicted_points': player_result.get('points', 0),
                                    'predicted_rebounds': player_result.get('rebounds', 0),
                                    'predicted_assists': player_result.get('assists', 0),
                                    'predicted_steals': player_result.get('steals', 0),
                                    'predicted_blocks': player_result.get('blocks', 0),
                                    'predicted_threes': player_result.get('threes', 0),
                                    'actual_points': player['points'],
                                    'actual_rebounds': player['rebounds'],
                                    'actual_assists': player['assists'],
                                    'actual_steals': player['steals'],
                                    'actual_blocks': player['blocks'],
                                    'actual_threes': player['threes']
                                }
                                predictions.append(prediction)
                                print(f"      ✅ Prediction obtained")
                                break
                        else:
                            print(f"      ❌ Player not found in results")
                    else:
                        print(f"      ❌ No predictions returned")
                        
                except Exception as e:
                    print(f"      ❌ Error: {e}")
        
        return predictions
        
    except Exception as e:
        print(f"❌ Service error: {e}")
        return []

def analyze_predictions(predictions):
    """Analyze how well our predictions matched reality"""
    
    if not predictions:
        print("❌ No predictions to analyze")
        return
    
    print(f"\n" + "=" * 80)
    print(f"📊 PREDICTION ACCURACY ANALYSIS")
    print("=" * 80)
    
    # Convert to DataFrame
    df = pd.DataFrame(predictions)
    
    # Calculate errors for each stat
    stats = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
    
    print(f"\n📈 INDIVIDUAL PLAYER RESULTS:")
    print("-" * 80)
    print(f"{'Player':<20} {'Stat':<8} {'Predicted':<10} {'Actual':<8} {'Error':<8} {'Abs Error':<10}")
    print("-" * 80)
    
    total_errors = {stat: [] for stat in stats}
    
    for _, row in df.iterrows():
        for stat in stats:
            predicted = row[f'predicted_{stat}']
            actual = row[f'actual_{stat}']
            error = predicted - actual
            abs_error = abs(error)
            
            total_errors[stat].append(abs_error)
            
            print(f"{row['name']:<20} {stat:<8} {predicted:<10.1f} {actual:<8.1f} {error:<8.1f} {abs_error:<10.1f}")
    
    # Calculate summary statistics
    print(f"\n📊 SUMMARY STATISTICS:")
    print("-" * 50)
    print(f"{'Stat':<10} {'MAE':<8} {'RMSE':<8} {'Mean Pred':<10} {'Mean Actual':<12}")
    print("-" * 50)
    
    for stat in stats:
        predicted_values = df[f'predicted_{stat}'].values
        actual_values = df[f'actual_{stat}'].values
        
        mae = np.mean(np.abs(predicted_values - actual_values))
        rmse = np.sqrt(np.mean((predicted_values - actual_values) ** 2))
        mean_pred = np.mean(predicted_values)
        mean_actual = np.mean(actual_values)
        
        print(f"{stat:<10} {mae:<8.2f} {rmse:<8.2f} {mean_pred:<10.1f} {mean_actual:<12.1f}")
    
    # Overall assessment
    print(f"\n🏆 OVERALL ASSESSMENT:")
    print("-" * 40)
    
    # Calculate average MAE across all stats
    all_maes = []
    for stat in stats:
        predicted_values = df[f'predicted_{stat}'].values
        actual_values = df[f'actual_{stat}'].values
        mae = np.mean(np.abs(predicted_values - actual_values))
        all_maes.append(mae)
    
    avg_mae = np.mean(all_maes)
    
    if avg_mae < 3.0:
        assessment = "🎉 EXCELLENT: Very accurate predictions!"
    elif avg_mae < 5.0:
        assessment = "✅ GOOD: Reasonably accurate predictions"
    elif avg_mae < 8.0:
        assessment = "⚠️ FAIR: Moderate accuracy, room for improvement"
    else:
        assessment = "❌ POOR: Significant prediction errors"
    
    print(f"Average MAE across all stats: {avg_mae:.2f}")
    print(f"{assessment}")
    
    # Check for systematic biases
    print(f"\n🔍 BIAS ANALYSIS:")
    print("-" * 30)
    
    for stat in stats:
        predicted_values = df[f'predicted_{stat}'].values
        actual_values = df[f'actual_{stat}'].values
        bias = np.mean(predicted_values - actual_values)
        
        if abs(bias) < 1.0:
            bias_status = "✅ Minimal bias"
        elif bias > 1.0:
            bias_status = f"⚠️ Over-predicting by {bias:.1f}"
        else:
            bias_status = f"⚠️ Under-predicting by {abs(bias):.1f}"
        
        print(f"{stat.capitalize()}: {bias:+.2f} - {bias_status}")

async def main():
    """Main execution"""
    
    print(f"🎯 Testing our models against last night's actual results")
    
    # Get actual results
    actual_results = get_actual_boxscores()
    total_players = sum(len(game['players']) for game in actual_results)
    print(f"📊 Loaded {len(actual_results)} games with {total_players} player performances")
    
    # Get our predictions
    predictions = await get_our_predictions(actual_results)
    
    if predictions:
        print(f"✅ Got predictions for {len(predictions)} players")
        
        # Analyze accuracy
        analyze_predictions(predictions)
        
        print(f"\n🎯 NEXT STEPS:")
        print(f"1. If accuracy is good: ✅ Models are working well!")
        print(f"2. If over-predicting: Models may need more regularization")
        print(f"3. If under-predicting: Models may be too conservative")
        print(f"4. Use these results to further improve the models")
    else:
        print(f"❌ No predictions obtained - check model functionality")

if __name__ == "__main__":
    asyncio.run(main())
