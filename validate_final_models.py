#!/usr/bin/env python3
"""
🎯 FINAL VALIDATION: Test the retrained models with enhanced regularization
Check if over-prediction issue is resolved while maintaining prediction diversity
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# Add project root to path
sys.path.append('.')

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

print("🎯 FINAL MODEL VALIDATION")
print("=" * 60)

def test_sample_players():
    """Test the models with sample WNBA players"""
    
    # Sample players from different skill levels
    test_players = [
        # Superstars
        {"name": "<PERSON><PERSON><PERSON><PERSON>", "team": "LAS", "tier": "superstar"},
        {"name": "<PERSON><PERSON><PERSON>", "team": "NY", "tier": "superstar"},
        {"name": "<PERSON><PERSON><PERSON><PERSON>", "team": "MIN", "tier": "superstar"},
        
        # Stars
        {"name": "<PERSON> Plum", "team": "LAS", "tier": "star"},
        {"name": "<PERSON>", "team": "NY", "tier": "star"},
        {"name": "<PERSON><PERSON>", "team": "<PERSON>", "tier": "star"},
        
        # Role Players
        {"name": "<PERSON>", "team": "LAS", "tier": "role"},
        {"name": "<PERSON> Carleton", "team": "MIN", "tier": "role"},
        {"name": "Alanna <PERSON>", "team": "MIN", "tier": "role"},
        
        # Bench Players
        {"name": "<PERSON> Hayes", "team": "LAS", "tier": "bench"},
        {"name": "Kayla McBride", "team": "MIN", "tier": "bench"},
        {"name": "Courtney Williams", "team": "MIN", "tier": "bench"}
    ]
    
    print(f"🏀 Testing {len(test_players)} players across different skill levels")
    print("-" * 60)
    
    try:
        # Initialize the unified prediction service
        service = UnifiedNeuralPredictionService()
        
        results = []
        
        for player in test_players:
            print(f"\n🔍 Testing {player['name']} ({player['tier']})...")
            
            try:
                # Create mock game data for testing
                game_data = {
                    'home_team': player['team'],
                    'away_team': 'TEST',
                    'league': 'WNBA',
                    'date': '2025-07-05'
                }

                # Create player data
                players_data = [{
                    'name': player['name'],
                    'team': player['team'],
                    'position': 'F',  # Mock position
                    'minutes_per_game': 25.0,  # Mock minutes
                    'is_starter': True if player['tier'] in ['superstar', 'star'] else False
                }]

                # Get predictions using the correct method
                import asyncio
                predictions = asyncio.run(service.predict_unified(
                    game_data=game_data,
                    players_data=players_data
                ))

                if predictions and hasattr(predictions, 'player_props') and predictions.player_props:
                    # Find our test player in the results
                    player_props = None
                    for player_result in predictions.player_props:
                        if player_result.get('name', '').lower() == player['name'].lower():
                            player_props = player_result
                            break

                    if player_props:
                        result = {
                            'name': player['name'],
                            'tier': player['tier'],
                            'points': player_props.get('points', 0),
                            'rebounds': player_props.get('rebounds', 0),
                            'assists': player_props.get('assists', 0),
                            'steals': player_props.get('steals', 0),
                            'blocks': player_props.get('blocks', 0),
                            'threes': player_props.get('threes', 0)
                        }

                        results.append(result)

                        print(f"   📊 Points: {result['points']:.1f}")
                        print(f"   📊 Rebounds: {result['rebounds']:.1f}")
                        print(f"   📊 Assists: {result['assists']:.1f}")
                        print(f"   📊 Steals: {result['steals']:.1f}")
                        print(f"   📊 Blocks: {result['blocks']:.1f}")
                        print(f"   📊 Threes: {result['threes']:.1f}")
                    else:
                        print(f"   ❌ Player not found in predictions")

                else:
                    print(f"   ❌ No player props returned")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return results
        
    except Exception as e:
        print(f"❌ Service initialization error: {e}")
        return []

def analyze_results(results):
    """Analyze the prediction results"""
    
    if not results:
        print("❌ No results to analyze")
        return
    
    print(f"\n" + "=" * 60)
    print(f"📊 PREDICTION ANALYSIS")
    print("=" * 60)
    
    # Convert to DataFrame for analysis
    df = pd.DataFrame(results)
    
    # Group by tier
    tier_stats = df.groupby('tier').agg({
        'points': ['mean', 'min', 'max', 'std'],
        'rebounds': ['mean', 'min', 'max', 'std'],
        'assists': ['mean', 'min', 'max', 'std'],
        'steals': ['mean', 'min', 'max', 'std'],
        'blocks': ['mean', 'min', 'max', 'std'],
        'threes': ['mean', 'min', 'max', 'std']
    }).round(1)
    
    print(f"\n📈 TIER-BASED ANALYSIS:")
    print("-" * 40)
    
    for tier in ['superstar', 'star', 'role', 'bench']:
        if tier in tier_stats.index:
            print(f"\n{tier.upper()} PLAYERS:")
            stats = tier_stats.loc[tier]
            
            for prop in ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']:
                mean_val = stats[(prop, 'mean')]
                min_val = stats[(prop, 'min')]
                max_val = stats[(prop, 'max')]
                std_val = stats[(prop, 'std')]
                
                print(f"  {prop.capitalize()}: {mean_val:.1f} (range: {min_val:.1f}-{max_val:.1f}, std: {std_val:.1f})")
    
    # Check for realistic bounds
    print(f"\n🎯 REALISTIC BOUNDS CHECK:")
    print("-" * 40)
    
    realistic_bounds = {
        'points': 28.0,
        'rebounds': 15.0,
        'assists': 10.0,
        'steals': 3.0,
        'blocks': 3.0,
        'threes': 4.0
    }
    
    for prop, max_realistic in realistic_bounds.items():
        max_predicted = df[prop].max()
        violations = (df[prop] > max_realistic).sum()
        
        status = "✅ GOOD" if violations == 0 else f"⚠️ {violations} violations"
        print(f"  {prop.capitalize()}: Max predicted = {max_predicted:.1f} (limit: {max_realistic}) - {status}")
    
    # Check for diversity
    print(f"\n🌈 PREDICTION DIVERSITY CHECK:")
    print("-" * 40)
    
    for prop in ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']:
        prop_range = df[prop].max() - df[prop].min()
        prop_std = df[prop].std()
        unique_values = df[prop].nunique()
        
        diversity_status = "✅ GOOD" if prop_range > 2.0 and unique_values > 6 else "⚠️ LOW"
        print(f"  {prop.capitalize()}: Range = {prop_range:.1f}, Std = {prop_std:.1f}, Unique = {unique_values} - {diversity_status}")
    
    # Overall assessment
    print(f"\n🏆 OVERALL ASSESSMENT:")
    print("-" * 40)
    
    # Check if all predictions are within realistic bounds
    all_realistic = all(
        df[prop].max() <= realistic_bounds[prop] 
        for prop in realistic_bounds.keys()
    )
    
    # Check if there's good diversity
    good_diversity = all(
        (df[prop].max() - df[prop].min()) > 2.0 and df[prop].nunique() > 6
        for prop in ['points', 'rebounds', 'assists']
    )
    
    if all_realistic and good_diversity:
        print("🎉 SUCCESS: Models produce realistic predictions with good diversity!")
    elif all_realistic:
        print("✅ PARTIAL SUCCESS: Realistic bounds achieved, but diversity may be low")
    elif good_diversity:
        print("⚠️ PARTIAL SUCCESS: Good diversity, but some over-predictions remain")
    else:
        print("❌ ISSUES: Both realism and diversity need improvement")

def main():
    """Main execution"""
    
    print(f"🎯 Testing retrained models with enhanced regularization")
    print(f"🔍 Checking for realistic bounds and prediction diversity")
    
    # Test the models
    results = test_sample_players()
    
    if results:
        # Analyze results
        analyze_results(results)
        
        print(f"\n🎯 NEXT STEPS:")
        print(f"1. If over-prediction is fixed: ✅ SUCCESS - models are ready!")
        print(f"2. If diversity is too low: Reduce regularization and retrain")
        print(f"3. If still over-predicting: Further increase regularization")
    else:
        print(f"\n❌ No results obtained - check model loading and service")

if __name__ == "__main__":
    main()
