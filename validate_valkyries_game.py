#!/usr/bin/env python3
"""
Validate our predictions against the actual Golden State Valkyries vs Minnesota Lynx game
"""

import sys
import os
import asyncio
import logging
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.abspath('.'))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def validate_valkyries_game():
    """Validate our scale fix against actual Valkyries vs Lynx game results"""
    try:
        print("🏀 VALIDATING GOLDEN STATE VALKYRIES vs MINNESOTA LYNX GAME")
        print("=" * 70)
        
        # Import services
        from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
        
        # Initialize service
        service = UnifiedNeuralPredictionService()
        await service._load_player_props_models()
        print(f"✅ Loaded {len(service.player_props_models)} player props models")
        
        # Game data
        game_data = {
            "home_team": "Minnesota Lynx",
            "away_team": "Golden State Valkyries", 
            "game_date": "2025-07-05",  # Assuming this was yesterday's game
            "league": "WNBA"
        }
        
        # Actual player stats from the box score
        actual_stats = {
            # Golden State Valkyries
            "<PERSON><PERSON>": {"points": 13, "rebounds": 10, "assists": 3, "steals": 2, "blocks": 0, "threes": 1},
            "<PERSON> Talbot": {"points": 10, "rebounds": 4, "assists": 4, "steals": 0, "blocks": 0, "threes": 2},
            "Temi Fagbenle": {"points": 7, "rebounds": 6, "assists": 2, "steals": 0, "blocks": 1, "threes": 0},
            "Tiffany Hayes": {"points": 23, "rebounds": 3, "assists": 4, "steals": 0, "blocks": 0, "threes": 5},
            "Veronica Burton": {"points": 6, "rebounds": 3, "assists": 4, "steals": 0, "blocks": 0, "threes": 1},
            "Monique Billings": {"points": 5, "rebounds": 4, "assists": 0, "steals": 0, "blocks": 0, "threes": 1},
            "Cecilia Zandalasini": {"points": 2, "rebounds": 0, "assists": 0, "steals": 0, "blocks": 0, "threes": 0},
            "Kate Martin": {"points": 5, "rebounds": 2, "assists": 1, "steals": 0, "blocks": 0, "threes": 1},
            
            # Minnesota Lynx
            "Bridget Carleton": {"points": 7, "rebounds": 4, "assists": 4, "steals": 0, "blocks": 0, "threes": 1},
            "Alanna Smith": {"points": 5, "rebounds": 5, "assists": 6, "steals": 1, "blocks": 2, "threes": 1},
            "Napheesa Collier": {"points": 22, "rebounds": 6, "assists": 1, "steals": 1, "blocks": 1, "threes": 1},
            "Kayla McBride": {"points": 12, "rebounds": 0, "assists": 3, "steals": 0, "blocks": 1, "threes": 2},
            "Courtney Williams": {"points": 15, "rebounds": 3, "assists": 2, "steals": 0, "blocks": 0, "threes": 3},
            "Jessica Shepard": {"points": 8, "rebounds": 10, "assists": 5, "steals": 2, "blocks": 0, "threes": 0},
            "Diamond Miller": {"points": 8, "rebounds": 0, "assists": 1, "steals": 0, "blocks": 0, "threes": 2},
            "Natisha Hiedeman": {"points": 5, "rebounds": 3, "assists": 3, "steals": 0, "blocks": 0, "threes": 1}
        }
        
        # Create player list for prediction
        test_players = []
        for player_name in actual_stats.keys():
            # Determine team and position (simplified)
            team = "Golden State Valkyries" if player_name in [
                "Kayla Thornton", "Stephanie Talbot", "Temi Fagbenle", "Tiffany Hayes", 
                "Veronica Burton", "Monique Billings", "Cecilia Zandalasini", "Kate Martin"
            ] else "Minnesota Lynx"
            
            # Simplified position assignment
            position = "F"  # Default to Forward
            if player_name in ["Veronica Burton", "Courtney Williams", "Kayla McBride", "Diamond Miller", "Natisha Hiedeman"]:
                position = "G"  # Guards
            elif player_name in ["Napheesa Collier", "Jessica Shepard", "Monique Billings", "Temi Fagbenle"]:
                position = "C"  # Centers
                
            test_players.append({
                "name": player_name,
                "team": team,
                "position": position
            })
        
        print(f"🧠 Getting predictions for {len(test_players)} players...")
        
        # Get our predictions
        result = await service.predict_unified(game_data, test_players)
        
        if not hasattr(result, 'player_props') or not result.player_props:
            print("❌ No player props predictions generated!")
            return
        
        # Analyze predictions vs actual results
        print("\n📊 PREDICTION vs ACTUAL COMPARISON")
        print("=" * 70)
        
        prop_types = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
        total_errors = {prop_type: [] for prop_type in prop_types}
        
        for player_name in actual_stats.keys():
            if player_name in result.player_props:
                print(f"\n👤 {player_name}:")
                predicted = result.player_props[player_name]
                actual = actual_stats[player_name]
                
                for prop_type in prop_types:
                    pred_val = predicted.get(prop_type, 0.0)
                    actual_val = actual.get(prop_type, 0)
                    error = abs(pred_val - actual_val)
                    total_errors[prop_type].append(error)
                    
                    status = "✅" if error <= 3 else "⚠️" if error <= 6 else "❌"
                    print(f"  {prop_type:8}: {pred_val:5.1f} vs {actual_val:2d} (error: {error:4.1f}) {status}")
            else:
                print(f"\n👤 {player_name}: ❌ No prediction generated")
        
        # Overall accuracy analysis
        print("\n" + "=" * 70)
        print("📈 OVERALL SCALE FIX VALIDATION")
        print("=" * 70)
        
        for prop_type in prop_types:
            if total_errors[prop_type]:
                errors = total_errors[prop_type]
                avg_error = sum(errors) / len(errors)
                max_error = max(errors)
                min_error = min(errors)
                accurate_predictions = sum(1 for e in errors if e <= 3)
                accuracy_rate = accurate_predictions / len(errors)
                
                print(f"\n{prop_type.upper()}:")
                print(f"  Average Error: {avg_error:.2f}")
                print(f"  Error Range: {min_error:.1f} - {max_error:.1f}")
                print(f"  Accuracy (±3): {accuracy_rate:.1%} ({accurate_predictions}/{len(errors)})")
                
                # Scale fix validation
                predicted_values = [result.player_props[name].get(prop_type, 0) for name in actual_stats.keys() if name in result.player_props]
                actual_values = [actual_stats[name].get(prop_type, 0) for name in actual_stats.keys()]
                
                if predicted_values:
                    pred_range = f"{min(predicted_values):.1f} - {max(predicted_values):.1f}"
                    actual_range = f"{min(actual_values)} - {max(actual_values)}"
                    print(f"  Predicted Range: {pred_range}")
                    print(f"  Actual Range: {actual_range}")
                    
                    # Check if predictions are in realistic ranges
                    realistic_ranges = {
                        'points': (0, 35), 'rebounds': (0, 15), 'assists': (0, 12),
                        'steals': (0, 5), 'blocks': (0, 5), 'threes': (0, 8)
                    }
                    min_realistic, max_realistic = realistic_ranges.get(prop_type, (0, 50))
                    
                    if all(min_realistic <= p <= max_realistic for p in predicted_values):
                        print(f"  ✅ All predictions in realistic range ({min_realistic}-{max_realistic})")
                    else:
                        unrealistic = [p for p in predicted_values if not (min_realistic <= p <= max_realistic)]
                        print(f"  ⚠️ {len(unrealistic)} unrealistic predictions: {unrealistic}")
        
        # Final scale fix assessment
        print("\n" + "=" * 70)
        print("🎯 SCALE FIX ASSESSMENT")
        print("=" * 70)
        
        all_predicted = []
        all_actual = []
        for player_name in actual_stats.keys():
            if player_name in result.player_props:
                for prop_type in prop_types:
                    all_predicted.append(result.player_props[player_name].get(prop_type, 0))
                    all_actual.append(actual_stats[player_name].get(prop_type, 0))
        
        if all_predicted:
            overall_avg_error = sum(abs(p - a) for p, a in zip(all_predicted, all_actual)) / len(all_predicted)
            print(f"Overall Average Error: {overall_avg_error:.2f}")
            print(f"Predicted Range: {min(all_predicted):.1f} - {max(all_predicted):.1f}")
            print(f"Actual Range: {min(all_actual)} - {max(all_actual)}")
            
            # Check for scale issues
            if max(all_predicted) > 100:
                print("❌ SCALE ISSUE: Predictions still in season total range!")
            elif all(0 <= p <= 50 for p in all_predicted):
                print("✅ SCALE FIX SUCCESS: All predictions in per-game range!")
            else:
                print("⚠️ MIXED RESULTS: Some predictions may have scale issues")
            
            # Check for diversity
            unique_predictions = len(set(all_predicted))
            if unique_predictions < len(all_predicted) * 0.5:
                print("⚠️ LOW DIVERSITY: Many identical predictions detected")
            else:
                print("✅ GOOD DIVERSITY: Predictions show variation")
        
        print(f"\n🏆 FINAL SCORE: Minnesota Lynx 82 - Golden State Valkyries 71")
        print("✅ Validation complete!")

    except Exception as e:
        print(f"❌ Error during validation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(validate_valkyries_game())
