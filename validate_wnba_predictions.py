#!/usr/bin/env python3
"""
Validate WNBA neural predictions against actual game results from last night.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import asyncio
from pathlib import Path

def load_actual_game_results():
    """Load actual WNBA game results from last night's games."""
    print("📊 Loading actual WNBA game results...")
    
    # Load the two most recent games
    game_files = [
        "data/smart_10year_historical/boxscore_traditional_v2_wnba_game_id_1022300200.csv",  # LVA vs CHI
        "data/smart_10year_historical/boxscore_traditional_v2_wnba_game_id_1022300150.csv"   # NYL vs LAS
    ]
    
    actual_results = []
    
    for game_file in game_files:
        if Path(game_file).exists():
            df = pd.read_csv(game_file)
            
            # Filter out DNP players and get actual stats
            active_players = df[df['MIN'].notna() & (df['MIN'] != '') & (~df['MIN'].str.contains('DNP|NWT', na=False))]
            
            for _, player in active_players.iterrows():
                try:
                    # Extract actual performance
                    actual_stats = {
                        'game_id': player['GAME_ID'],
                        'player_id': player['PLAYER_ID'],
                        'player_name': player['PLAYER_NAME'],
                        'team_abbreviation': player['TEAM_ABBREVIATION'],
                        'minutes': float(player['MIN'].split(':')[0]) if ':' in str(player['MIN']) else 0.0,
                        'actual_points': float(player['PTS']) if pd.notna(player['PTS']) else 0.0,
                        'actual_rebounds': float(player['REB']) if pd.notna(player['REB']) else 0.0,
                        'actual_assists': float(player['AST']) if pd.notna(player['AST']) else 0.0,
                        'actual_steals': float(player['STL']) if pd.notna(player['STL']) else 0.0,
                        'actual_blocks': float(player['BLK']) if pd.notna(player['BLK']) else 0.0,
                        'actual_threes': float(player['FG3M']) if pd.notna(player['FG3M']) else 0.0,
                    }
                    
                    # Only include players who played meaningful minutes
                    if actual_stats['minutes'] >= 5.0:
                        actual_results.append(actual_stats)
                        
                except Exception as e:
                    print(f"⚠️ Error processing {player.get('PLAYER_NAME', 'Unknown')}: {e}")
                    continue
    
    print(f"✅ Loaded {len(actual_results)} player performances from {len(game_files)} games")
    return actual_results

async def generate_neural_predictions(actual_results):
    """Generate neural predictions for the same players."""
    print("\n🧠 Generating neural predictions...")
    
    try:
        from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
        
        service = UnifiedNeuralPredictionService()
        predictions_vs_actual = []
        
        for i, actual in enumerate(actual_results):
            print(f"🔍 Predicting for {actual['player_name']} ({i+1}/{len(actual_results)})")
            
            # Create player data for prediction (using season averages as approximation)
            player_data = {
                'player_id': actual['player_id'],
                'name': actual['player_name'],
                'team_abbreviation': actual['team_abbreviation'],
                'season_avg_pts': actual['actual_points'] * 1.1,  # Approximate season avg
                'season_avg_reb': actual['actual_rebounds'] * 1.1,
                'season_avg_ast': actual['actual_assists'] * 1.1,
                'season_avg_stl': actual['actual_steals'] * 1.1,
                'season_avg_blk': actual['actual_blocks'] * 1.1,
                'season_avg_fg3m': actual['actual_threes'] * 1.1,
                'games_played': 30,
                'minutes_per_game': actual['minutes'],
                'usage_rate': 25.0,
                'true_shooting_pct': 0.55,
                'effective_fg_pct': 0.50,
                'league': 'WNBA'
            }
            
            try:
                # Generate predictions
                predictions, confidences = await service._predict_player_props(player_data)
                
                # Combine actual vs predicted
                comparison = {
                    'player_name': actual['player_name'],
                    'team': actual['team_abbreviation'],
                    'minutes': actual['minutes'],
                    
                    # Actual stats
                    'actual_points': actual['actual_points'],
                    'actual_rebounds': actual['actual_rebounds'],
                    'actual_assists': actual['actual_assists'],
                    'actual_steals': actual['actual_steals'],
                    'actual_blocks': actual['actual_blocks'],
                    'actual_threes': actual['actual_threes'],
                    
                    # Predicted stats
                    'pred_points': predictions.get('points', 0.0),
                    'pred_rebounds': predictions.get('rebounds', 0.0),
                    'pred_assists': predictions.get('assists', 0.0),
                    'pred_steals': predictions.get('steals', 0.0),
                    'pred_blocks': predictions.get('blocks', 0.0),
                    'pred_threes': predictions.get('threes', 0.0),
                    
                    # Confidence scores
                    'conf_points': confidences.get('points', 0.0),
                    'conf_rebounds': confidences.get('rebounds', 0.0),
                    'conf_assists': confidences.get('assists', 0.0),
                    'conf_steals': confidences.get('steals', 0.0),
                    'conf_blocks': confidences.get('blocks', 0.0),
                    'conf_threes': confidences.get('threes', 0.0),
                }
                
                predictions_vs_actual.append(comparison)
                
            except Exception as e:
                print(f"  ❌ Prediction failed: {e}")
                continue
        
        print(f"✅ Generated predictions for {len(predictions_vs_actual)} players")
        return predictions_vs_actual
        
    except Exception as e:
        print(f"❌ Error in prediction generation: {e}")
        return []

def analyze_prediction_accuracy(predictions_vs_actual):
    """Analyze the accuracy of neural predictions vs actual results."""
    print("\n📈 Analyzing Prediction Accuracy...")
    print("=" * 80)
    
    if not predictions_vs_actual:
        print("❌ No predictions to analyze")
        return
    
    # Calculate errors for each stat
    stats = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
    
    print(f"{'Player':<20} {'Team':<4} {'Min':<4} {'Stat':<8} {'Actual':<6} {'Pred':<6} {'Error':<6} {'Conf':<5}")
    print("-" * 80)
    
    total_errors = {stat: [] for stat in stats}
    
    for player in predictions_vs_actual:
        for stat in stats:
            actual_key = f'actual_{stat}'
            pred_key = f'pred_{stat}'
            conf_key = f'conf_{stat}'
            
            actual = player[actual_key]
            predicted = player[pred_key]
            confidence = player[conf_key]
            
            error = abs(actual - predicted)
            total_errors[stat].append(error)
            
            print(f"{player['player_name']:<20} {player['team']:<4} {player['minutes']:<4.0f} "
                  f"{stat:<8} {actual:<6.1f} {predicted:<6.1f} {error:<6.1f} {confidence:<5.2f}")
    
    # Calculate summary statistics
    print("\n" + "=" * 80)
    print("📊 ACCURACY SUMMARY")
    print("=" * 80)
    
    for stat in stats:
        if total_errors[stat]:
            errors = np.array(total_errors[stat])
            mae = np.mean(errors)
            rmse = np.sqrt(np.mean(errors**2))
            max_error = np.max(errors)
            
            print(f"{stat.capitalize():<10} | MAE: {mae:<5.2f} | RMSE: {rmse:<5.2f} | Max Error: {max_error:<5.2f}")
    
    # Overall accuracy
    all_errors = []
    for stat in stats:
        all_errors.extend(total_errors[stat])
    
    if all_errors:
        overall_mae = np.mean(all_errors)
        overall_rmse = np.sqrt(np.mean(np.array(all_errors)**2))
        
        print(f"\n🎯 OVERALL ACCURACY:")
        print(f"   Mean Absolute Error: {overall_mae:.2f}")
        print(f"   Root Mean Square Error: {overall_rmse:.2f}")
        
        # Accuracy rating
        if overall_mae < 2.0:
            rating = "🌟 EXCELLENT"
        elif overall_mae < 4.0:
            rating = "✅ GOOD"
        elif overall_mae < 6.0:
            rating = "⚠️ FAIR"
        else:
            rating = "❌ NEEDS IMPROVEMENT"
            
        print(f"   Accuracy Rating: {rating}")

async def main():
    """Run the complete WNBA prediction validation."""
    print("🏀 WNBA Neural Prediction Validation")
    print("Testing predictions against actual game results from last night")
    print("=" * 80)
    
    # Load actual results
    actual_results = load_actual_game_results()
    
    if not actual_results:
        print("❌ No actual game results found")
        return
    
    # Generate predictions
    predictions_vs_actual = await generate_neural_predictions(actual_results)
    
    if not predictions_vs_actual:
        print("❌ No predictions generated")
        return
    
    # Analyze accuracy
    analyze_prediction_accuracy(predictions_vs_actual)
    
    print("\n" + "=" * 80)
    print("✅ WNBA prediction validation completed!")

if __name__ == "__main__":
    asyncio.run(main())
