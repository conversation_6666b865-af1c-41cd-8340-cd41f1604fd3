#!/usr/bin/env python3
"""
✅ VERIFY FEATURE LIST FIX
==========================

This script verifies that the retrained WNBA models now have the proper
feature_list and can be used for inference without the all-zero input bug.
"""

import torch
from pathlib import Path

def verify_feature_list_fix():
    """Verify that the feature list fix worked"""
    
    print("✅ VERIFYING FEATURE LIST FIX")
    print("=" * 50)
    
    # Check all WNBA models
    models_dir = Path("models/real_basketball_models")
    prop_types = ["points", "rebounds", "assists", "steals", "blocks", "threes"]
    
    all_fixed = True
    
    for prop_type in prop_types:
        model_path = models_dir / f"best_{prop_type}_model.pt"
        
        print(f"\n🔍 Checking {prop_type} model...")
        
        if not model_path.exists():
            print(f"❌ Model not found: {model_path}")
            all_fixed = False
            continue
        
        try:
            # Load checkpoint
            checkpoint = torch.load(model_path, map_location='cpu')
            
            # Check for feature_list
            has_feature_list = 'feature_list' in checkpoint
            has_scaler_params = 'feature_scaler_params' in checkpoint
            
            print(f"  ✅ feature_list: {'✓' if has_feature_list else '✗'}")
            print(f"  ✅ feature_scaler_params: {'✓' if has_scaler_params else '✗'}")
            
            if has_feature_list:
                feature_list = checkpoint['feature_list']
                print(f"  📊 Feature count: {len(feature_list)}")
                print(f"  📋 Sample features: {feature_list[:3]}...")
            else:
                print(f"  ❌ FEATURE LIST MISSING!")
                all_fixed = False
            
            if has_scaler_params:
                scaler_params = checkpoint['feature_scaler_params']
                print(f"  🔧 Scaler keys: {list(scaler_params.keys())}")
            else:
                print(f"  ❌ SCALER PARAMS MISSING!")
                all_fixed = False
                
        except Exception as e:
            print(f"  ❌ Error loading model: {e}")
            all_fixed = False
    
    print("\n" + "=" * 50)
    if all_fixed:
        print("🎉 FEATURE LIST FIX SUCCESSFUL!")
        print("✅ All WNBA models now have proper feature_list")
        print("🚀 Inference will now receive real features instead of all-zero inputs")
        print("💯 Models are ready for production use")
    else:
        print("❌ FEATURE LIST FIX INCOMPLETE!")
        print("⚠️ Some models still missing required components")
    
    return all_fixed

if __name__ == "__main__":
    verify_feature_list_fix()
