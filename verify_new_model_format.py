#!/usr/bin/env python3
"""
🔍 VERIFY NEW MODEL FORMAT
==========================

This script verifies that the newly trained WNBA models have the proper new format
with feature_list and feature_scaler_params for production inference.
"""

import torch
from pathlib import Path

def verify_model_format(model_path: str, prop_type: str) -> dict:
    """Verify that a model has the new format with all required components"""
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        
        # Check for required new format components
        has_feature_list = 'feature_list' in checkpoint
        has_scaler_params = 'feature_scaler_params' in checkpoint
        has_config = 'config' in checkpoint
        has_model_state = 'model_state_dict' in checkpoint
        
        result = {
            'prop_type': prop_type,
            'model_path': model_path,
            'has_feature_list': has_feature_list,
            'has_scaler_params': has_scaler_params,
            'has_config': has_config,
            'has_model_state': has_model_state,
            'is_valid_new_format': has_feature_list and has_scaler_params and has_config and has_model_state
        }
        
        # Get additional details if available
        if has_feature_list:
            result['feature_count'] = len(checkpoint['feature_list'])
            result['sample_features'] = checkpoint['feature_list'][:5]  # First 5 features
        
        if has_scaler_params:
            scaler_params = checkpoint['feature_scaler_params']
            result['scaler_keys'] = list(scaler_params.keys())
        
        if has_config:
            config = checkpoint['config']
            result['prop_type_config'] = config.get('prop_type', 'N/A')
            result['league'] = config.get('league', 'N/A')
            result['input_dim'] = config.get('input_dim', 'N/A')
        
        return result
        
    except Exception as e:
        return {
            'prop_type': prop_type,
            'model_path': model_path,
            'error': str(e),
            'is_valid_new_format': False
        }

def main():
    """Main verification function"""
    print("🔍 VERIFYING NEW WNBA MODEL FORMAT")
    print("=" * 50)
    
    # WNBA models to verify
    models_dir = Path("models/real_basketball_models")
    wnba_models = [
        ('points', 'best_points_model.pt'),
        ('rebounds', 'best_rebounds_model.pt'),
        ('assists', 'best_assists_model.pt'),
        ('steals', 'best_steals_model.pt'),
        ('blocks', 'best_blocks_model.pt'),
        ('threes', 'best_threes_model.pt')
    ]
    
    all_valid = True
    results = []
    
    for prop_type, model_file in wnba_models:
        model_path = models_dir / model_file
        
        print(f"\n🔍 Verifying {prop_type} model...")
        print(f"📁 Path: {model_path}")
        
        if not model_path.exists():
            print(f"❌ Model file not found!")
            all_valid = False
            continue
        
        result = verify_model_format(str(model_path), prop_type)
        results.append(result)
        
        if result.get('error'):
            print(f"❌ Error loading model: {result['error']}")
            all_valid = False
            continue
        
        # Display verification results
        print(f"✅ feature_list: {'✓' if result['has_feature_list'] else '✗'}")
        print(f"✅ feature_scaler_params: {'✓' if result['has_scaler_params'] else '✗'}")
        print(f"✅ config: {'✓' if result['has_config'] else '✗'}")
        print(f"✅ model_state_dict: {'✓' if result['has_model_state'] else '✗'}")
        
        if result['has_feature_list']:
            print(f"📊 Features: {result['feature_count']}")
            print(f"📋 Sample features: {result['sample_features']}")
        
        if result['has_scaler_params']:
            print(f"🔧 Scaler params: {result['scaler_keys']}")
        
        if result['has_config']:
            print(f"⚙️ Config - Prop: {result['prop_type_config']}, League: {result['league']}, Input dim: {result['input_dim']}")
        
        if result['is_valid_new_format']:
            print(f"🎉 {prop_type} model has VALID NEW FORMAT!")
        else:
            print(f"⚠️ {prop_type} model is missing required components")
            all_valid = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 50)
    
    valid_models = [r for r in results if r.get('is_valid_new_format', False)]
    
    print(f"✅ Valid models: {len(valid_models)}/{len(wnba_models)}")
    
    if valid_models:
        print("🎯 Models with new format:")
        for result in valid_models:
            print(f"  ✓ {result['prop_type']}")
    
    invalid_models = [r for r in results if not r.get('is_valid_new_format', False)]
    if invalid_models:
        print("❌ Models needing attention:")
        for result in invalid_models:
            print(f"  ✗ {result['prop_type']}")
    
    if all_valid:
        print("\n🎉 ALL WNBA MODELS HAVE VALID NEW FORMAT!")
        print("🚀 Ready for production inference with proper feature alignment!")
        print("✅ UnifiedNeuralPredictionService can now use PlayerPropsTrainingPipeline.predict_from_checkpoint")
    else:
        print("\n⚠️ Some models need attention before production use")
    
    print(f"\n📁 Models location: {models_dir}")
    print("🔧 These models include feature_list and feature_scaler_params for proper inference")

if __name__ == "__main__":
    main()
