#!/usr/bin/env python3
"""
Test script to verify WNBA neural prediction system is working correctly.
Tests both game predictions and player props predictions.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
import pandas as pd

import asyncio

def test_wnba_player_props():
    """Test WNBA player props predictions with real player data."""
    print("🏀 Testing WNBA Player Props Predictions...")

    # Initialize the service
    service = UnifiedNeuralPredictionService()

    # Test data from the WNBA boxscore - A'ja <PERSON> (star player)
    test_player_data = {
        'player_id': 1628932,
        'player_name': "A'ja <PERSON>",
        'team_id': 1611661319,
        'team_abbreviation': 'LVA',
        'season_avg_pts': 22.8,
        'season_avg_reb': 11.9,
        'season_avg_ast': 2.3,
        'season_avg_stl': 1.8,
        'season_avg_blk': 2.6,
        'season_avg_fg3m': 0.4,
        'games_played': 38,
        'minutes_per_game': 34.6,
        'usage_rate': 28.5,
        'true_shooting_pct': 0.578,
        'effective_fg_pct': 0.521,
        'league': 'WNBA'
    }

    # Test Kelsey Plum (another star player)
    test_player_data_2 = {
        'player_id': 1628276,
        'player_name': "Kelsey Plum",
        'team_id': 1611661319,
        'team_abbreviation': 'LVA',
        'season_avg_pts': 17.8,
        'season_avg_reb': 2.9,
        'season_avg_ast': 4.2,
        'season_avg_stl': 1.1,
        'season_avg_blk': 0.2,
        'season_avg_fg3m': 2.8,
        'games_played': 40,
        'minutes_per_game': 32.1,
        'usage_rate': 24.2,
        'true_shooting_pct': 0.612,
        'effective_fg_pct': 0.548,
        'league': 'WNBA'
    }

    # Game data for unified prediction
    game_data = {
        'home_team_id': 1611661319,
        'away_team_id': 1611661314,
        'home_team_abbreviation': 'LVA',
        'away_team_abbreviation': 'CHI',
        'league': 'WNBA'
    }

    players_to_test = [
        ("A'ja Wilson", test_player_data),
        ("Kelsey Plum", test_player_data_2)
    ]

    for player_name, player_data in players_to_test:
        print(f"\n📊 Testing predictions for {player_name}:")

        try:
            # Use the unified prediction method
            async def run_prediction():
                result = await service.predict_unified(game_data, [player_data])
                return result

            # Run the async prediction
            result = asyncio.run(run_prediction())

            # Extract player props from result
            if hasattr(result, 'player_props') and result.player_props:
                player_props = result.player_props[0]  # First player

                for prop_name, prop_data in player_props.items():
                    if isinstance(prop_data, dict):
                        value = prop_data.get('prediction', 'N/A')
                        confidence = prop_data.get('confidence', 'N/A')

                        print(f"  {prop_name.capitalize()}: {value} (confidence: {confidence})")

                        # Validate prediction is reasonable for WNBA
                        if prop_name == 'points' and isinstance(value, (int, float)):
                            if not (0 <= value <= 40):
                                print(f"    ⚠️  WARNING: {prop_name} prediction {value} seems unrealistic for WNBA")
                        elif prop_name == 'rebounds' and isinstance(value, (int, float)):
                            if not (0 <= value <= 20):
                                print(f"    ⚠️  WARNING: {prop_name} prediction {value} seems unrealistic for WNBA")
                        elif prop_name == 'assists' and isinstance(value, (int, float)):
                            if not (0 <= value <= 15):
                                print(f"    ⚠️  WARNING: {prop_name} prediction {value} seems unrealistic for WNBA")
                        elif prop_name in ['steals', 'blocks', 'threes'] and isinstance(value, (int, float)):
                            if not (0 <= value <= 10):
                                print(f"    ⚠️  WARNING: {prop_name} prediction {value} seems unrealistic for WNBA")
            else:
                print("  ❌ No player props found in result")

        except Exception as e:
            print(f"❌ Error testing {player_name}: {str(e)}")

def main():
    """Run all WNBA prediction tests."""
    print("🚀 Starting WNBA Neural Prediction System Tests...")
    print("=" * 60)
    
    # Test player props
    test_wnba_player_props()
    
    print("\n" + "=" * 60)
    print("✅ WNBA prediction tests completed!")
    print("\nNote: These tests verify that:")
    print("1. Models load without errors")
    print("2. Predictions are generated successfully") 
    print("3. Output values are within realistic WNBA ranges")
    print("4. The fixed neural pipeline is working correctly")

if __name__ == "__main__":
    main()
